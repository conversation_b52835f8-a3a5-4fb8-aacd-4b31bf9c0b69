# 本地开发环境：本地启动所有项目（前端、后端、APP）时使用，不依赖外部环境
NODE_ENV=development

VITE_DEV=true

# 请求路径
# 测试环境
# VITE_BASE_URL='http://192.168.135.102:48080'
# lxh 本地
# VITE_BASE_URL='http://192.168.135.98:48080'
# yh 本地
VITE_BASE_URL='http://192.168.135.99:48080'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=true

# 打包路径
VITE_BASE_PATH=/

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false

VITE_OPEN=false

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'