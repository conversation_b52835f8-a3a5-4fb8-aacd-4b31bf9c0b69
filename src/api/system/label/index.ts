import request from '@/config/axios'

/**
 * cn.governance.pangu.module.data.controller.admin.labelconf.vo.LabelConfRespVO
 *
 * LabelConfRespVO
 */
export interface LabelConfRespVO {
    /**
     * 该分类下标签数量
     */
    count?: number;
    /**
     * 创建时间
     */
    createTime?: string;
    /**
     * 逻辑主键
     */
    id?: number;
    /**
     * 名称
     */
    name?: string;
    /**
     * 排序
     */
    sort?: number;
    /**
     * 0-默认 1-自定义
     */
    type?: number;
    [property: string]: any;
}

/**
 * 获取标签配置列表
 * @param {PageParam} params - 分页查询参数
 * @returns {Promise<PageResult<any>>} 标签配置列表
 * @see https://app.apifox.com/link/project/6505154/apis/api-305076064
 */
export const getLabelConfigList = async (params: PageParam) : Promise<LabelConfRespVO[]> => await request.get({ url: '/data/label-conf/list', params })
