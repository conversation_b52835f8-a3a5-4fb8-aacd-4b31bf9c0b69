// 引入unocss css
import '@/plugins/unocss'

// 导入全局的svg图标
import '@/plugins/svgIcon'
import 'uno.css'
// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 element-plus
// import { setupElementPlus } from '@/plugins/elementPlus'

// 引入 form-create
// import { setupFormCreate } from '@/plugins/formCreate'
// import ElementPlus from 'element-plus'  //引入element-plus库
// import VForm3 from 'vform3-builds'
import 'vform3-builds/dist/designer.style.css' // 确保引入样式文件
import 'element-plus/dist/index.css' // ✅ 必须引入
// 引入全局样式
import '@/styles/index.scss'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from '@/router'

// 指令
import { setupAuth, setupMountedFocus } from '@/directives'

import { createApp } from 'vue'

import App from './App.vue'

import './permission'

// import '@/plugins/tongji' // 百度统计
// import Logger from '@/utils/Logger'

import VueDOMPurifyHTML from 'vue-dompurify-html' // 解决v-html 的安全隐患

// import { ElTable, ElTableColumn } from 'element-plus'

// // 全局修改 ElTable 组件默认属性
// ElTable.props.border = { type: Boolean, default: true } // 默认开启表格边框（启用列宽拖拽）
// ElTableColumn.props.resizable = { type: Boolean, default: true } // （可选）确保列支持拖拽

import { mockXHR } from './mock'
mockXHR()

// 创建实例
const setupAll = async () => {
  const app = createApp(App)

  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  // setupElementPlus(app)

  // setupFormCreate(app)

  setupRouter(app)

  // directives 指令
  setupAuth(app)
  setupMountedFocus(app)

  await router.isReady()

  app.use(VueDOMPurifyHTML)
  // app.use(VForm3)
  // app.use(ElementPlus)
  app.mount('#app')
}

setupAll()

// Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
