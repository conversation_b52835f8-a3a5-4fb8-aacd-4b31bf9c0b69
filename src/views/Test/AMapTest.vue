<template>
  <div class="amap-test-page">
    <div class="test-container">
      <h2>高德地图组件测试页面</h2>
      
      <div class="test-section">
        <h3>基础功能测试</h3>
        <div class="form-item">
          <label>选择位置：</label>
          <AMapSelector
            v-model="testLocation"
            placeholder="点击选择位置"
            @change="handleLocationChange"
          />
        </div>
        
        <div v-if="testLocation.address" class="result-info">
          <h4>选中的位置：</h4>
          <p><strong>地址：</strong>{{ testLocation.address }}</p>
          <p><strong>经度：</strong>{{ testLocation.lng }}</p>
          <p><strong>纬度：</strong>{{ testLocation.lat }}</p>
          <p v-if="testLocation.name"><strong>名称：</strong>{{ testLocation.name }}</p>
        </div>
      </div>

      <div class="test-section">
        <h3>配置信息</h3>
        <div class="config-info">
          <p><strong>API密钥状态：</strong>
            <span :class="keyStatus.valid ? 'status-success' : 'status-error'">
              {{ keyStatus.message }}
            </span>
          </p>
          <p><strong>默认中心点：</strong>{{ defaultCenter.join(', ') }}</p>
          <p><strong>默认缩放级别：</strong>{{ defaultZoom }}</p>
        </div>
      </div>

      <div class="test-section">
        <h3>操作按钮</h3>
        <div class="button-group">
          <el-button @click="resetLocation">重置位置</el-button>
          <el-button @click="setRandomLocation">设置随机位置</el-button>
          <el-button @click="copyLocation" :disabled="!testLocation.lng">复制坐标</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { amapConfig, validateAmapKey, getAmapKey } from '@/config/map'
import type { LocationData } from '@/components/AMapSelector'

// 响应式数据
const testLocation = ref<LocationData>({
  lng: 0,
  lat: 0,
  address: ''
})

const keyStatus = reactive({
  valid: false,
  message: '检查中...'
})

const defaultCenter = amapConfig.defaultOptions.center
const defaultZoom = amapConfig.defaultOptions.zoom

// 位置改变处理
const handleLocationChange = (location: LocationData) => {
  console.log('位置已选择:', location)
  ElMessage.success(`位置已选择: ${location.address}`)
}

// 重置位置
const resetLocation = () => {
  testLocation.value = {
    lng: 0,
    lat: 0,
    address: ''
  }
  ElMessage.info('位置已重置')
}

// 设置随机位置（湖州周边）
const setRandomLocation = () => {
  const baseLng = 120.088
  const baseLat = 30.86
  const randomLng = baseLng + (Math.random() - 0.5) * 0.1
  const randomLat = baseLat + (Math.random() - 0.5) * 0.1
  
  testLocation.value = {
    lng: randomLng,
    lat: randomLat,
    address: `随机位置 (${randomLng.toFixed(6)}, ${randomLat.toFixed(6)})`
  }
  ElMessage.success('已设置随机位置')
}

// 复制坐标
const copyLocation = async () => {
  if (!testLocation.value.lng) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  const text = `${testLocation.value.lng}, ${testLocation.value.lat}`
  
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('坐标已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('坐标已复制到剪贴板')
  }
}

// 检查API密钥状态
const checkApiKey = () => {
  try {
    if (validateAmapKey()) {
      const key = getAmapKey()
      keyStatus.valid = true
      keyStatus.message = `配置正确 (${key.substring(0, 8)}...)`
    } else {
      keyStatus.valid = false
      keyStatus.message = '未配置或无效'
    }
  } catch (error) {
    keyStatus.valid = false
    keyStatus.message = `错误: ${error.message}`
  }
}

// 组件挂载时检查配置
onMounted(() => {
  checkApiKey()
})
</script>

<style scoped lang="scss">
.amap-test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  
  .test-container {
    h2 {
      color: #303133;
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }
    
    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      background: #fff;
      
      h3 {
        color: #409eff;
        margin-bottom: 15px;
        font-size: 16px;
      }
      
      .form-item {
        margin-bottom: 15px;
        
        label {
          display: inline-block;
          width: 100px;
          font-weight: 500;
          color: #606266;
        }
      }
      
      .result-info {
        margin-top: 15px;
        padding: 15px;
        background: #f5f7fa;
        border-radius: 4px;
        border-left: 4px solid #409eff;
        
        h4 {
          color: #303133;
          margin-bottom: 10px;
          font-size: 14px;
        }
        
        p {
          margin: 5px 0;
          color: #606266;
          font-size: 14px;
          
          strong {
            color: #303133;
          }
        }
      }
      
      .config-info {
        p {
          margin: 10px 0;
          color: #606266;
          
          strong {
            color: #303133;
          }
          
          .status-success {
            color: #67c23a;
            font-weight: 500;
          }
          
          .status-error {
            color: #f56c6c;
            font-weight: 500;
          }
        }
      }
      
      .button-group {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    }
  }
}
</style>
