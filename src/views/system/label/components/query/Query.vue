<template>
  <div  v-loading="isLoading">
    <div class="flex justify-end mb-2">
      <el-button type="primary" @click="showDialog = true">添加</el-button>
      <el-button type="danger" @click="removeSelected" :disabled="!selectedRowKeys.length"
        >删除</el-button
      >
      <el-button @click="showPreviewDialog = true">预览</el-button>
    </div>
    <el-table
      row-key="uuid"
      ref="tableRef"
      class="query-sortable-table-container"
      :data="tableData"
      border
      style="width: 100%"
      height="640"
      @selection-change="onSelectionChange"
      :row-class-name="tableRowClassName"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column type="selection" width="40" />
      <el-table-column label="查询字段" prop="label">
        <template #default="{ row }">
          <div class="flex flex-col">
            <span>{{ row.field.map(f => f.name).join('、') }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提示文字" prop="fieldType"  width="260">
        <template #default="{ row }">
          <el-input  v-model="row.hint" size="small" placeholder="请输入提示文字" />
        </template>
      </el-table-column>
      <el-table-column label="查询类型" prop="queryType" width="270">
        <template #default="{ row }">
          <el-radio-group v-model="row.queryType">
            <el-radio v-if="row.fieldType === FieldType.TEXT || row.fieldType === FieldType.NUMBER" :value="0"
              >搜索</el-radio
            >
            <template v-else-if="row.fieldType === FieldType.RADIO || row.fieldType === FieldType.CHECKBOX || row.fieldType === FieldType.REGION || row.fieldType === FieldType.TAG">
              <el-radio :value="1">单选</el-radio>
              <el-radio :value="2">多选</el-radio>
            </template>
            <template v-else-if="row.fieldType === FieldType.DATE || row.fieldType === FieldType.DATE_RANGE">
              <el-radio :value="3">时间</el-radio>
              <el-radio :value="4">时间区间</el-radio>
            </template>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column label="默认值" prop="defaultValue" width="280">
        <template #default="{ row }">
          <!-- 搜索：输入框 -->
          <el-input v-if="row.queryType === 0" v-model="row.defaultValue" class="w-full" size="small" placeholder="请输入默认值" />
          <!-- 多选、单选、区域、标签 -->
           <template v-if="row.fieldType === FieldType.RADIO || row.fieldType === FieldType.CHECKBOX || row.fieldType === FieldType.REGION || row.fieldType === FieldType.TAG">
            <template v-if="row.fieldType === FieldType.RADIO || row.fieldType === FieldType.CHECKBOX ">
              <el-select
                 v-if="row.queryType === 1 || row.queryType === 2"
                 v-model="row.defaultValue"
                 size="small"
                 :multiple="row.queryType === 2"
                 placeholder="请选择"
                 class="w-full"
               >
                 <el-option
                   v-for="opt in row.selectedOptions"
                   :key="opt.dictType"
                   :label="opt.label"
                   :value="opt.value"
                 /></el-select>
            </template>
            <template v-else>
              <!-- 区域为树形单选 -->
              <el-tree-select
                v-model="row.defaultValue"
                :data="deptList"
                :multiple="row.queryType === 2"
                check-strictly
                :render-after-expand="false"
                check-on-click-node
                class="!w-240px"
                :props="{
                  ...defaultProps,
                  children: 'childList',
                  label: 'name'
                }"
              />
            </template>
           </template>
          <!-- 时间/时间区间：时间选择器 -->
          <el-date-picker
            v-else-if="row.queryType === 3"
            v-model="row.defaultValue"
            type="date"
            placeholder="请选择日期"
            size="small"
             class="w-full"
          />
          <el-date-picker
            v-else-if="row.queryType === 4"
            v-model="row.defaultValue"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            class="w-full"
          />
          <div v-else></div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" align="center">
        <template #default="{ row }">
          <el-button link type="primary" :disabled="row.queryType !== 0" @click="openSubFieldDialog(row)">
            添加字段
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="排序" width="120" align="center">
        <template #default="">
          <Icon icon="ep:rank" class="text-red-500 cursor-pointer" />
        </template>
      </el-table-column>
    </el-table>

    <!-- 主字段选择弹窗 -->
    <FieldSelectDialog
      v-model="showDialog"
      :fieldList="allFields"
      :selectedKeys="tableData.map((i) => i.uuid!)"
      @confirm="addFields"
    />
    <!-- 子字段选择弹窗 -->
    <SubFieldSelectDialog
      v-model="showSubFieldDialog"
      :field-list="allFields"
      :excluded-keys="subFieldExcludedKeys"
      :selected-keys-prop="currentRow?.field?.map((f) => f.uuid!) || []"
      @confirm="addSubFields"
    />
    <!-- 预览弹窗 -->
    <el-dialog v-model="showPreviewDialog" title="预览" width="80%">
      <QueryPreview :query-fields="tableData" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Sortable from 'sortablejs'
import type { ElTable } from 'element-plus'
import * as FieldConfApi from '@/api/system/data/field-conf'
import * as LabelManageApi from '@/api/system/data/label-manage'
import * as QueryConfApi from '@/api/system/data/query-conf'
import * as DictDataApi from '@/api/system/dict/dict.data'
import FieldSelectDialog from '../common/FieldSelectDialog.vue'
import SubFieldSelectDialog from './SubFieldSelectDialog.vue'
import QueryPreview from './QueryPreview.vue'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'
import { FieldType } from '@/config/constants/enums/field'
import { handleTree2, defaultProps } from '@/utils/tree'

// 单选、多选、区域、标签为单选或者多选  日期和日期区间为日期或者日期区间  文本和数字可以多个字段合并查询
const fieldTypeLabelMap = {
  [FieldType.TEXT]: 0,
  [FieldType.NUMBER]: 0,
  [FieldType.RADIO]: 1,
  [FieldType.CHECKBOX]: 1,
  [FieldType.DATE]: 3,
  [FieldType.DATE_RANGE]: 4,
  [FieldType.ADDRESS]: 0,
  [FieldType.REGION]: 1,
  [FieldType.TAG]: 1,
  [FieldType.ATTACHMENT]: 0,
}

const { query } = useRoute() // 查询参数
const emits = defineEmits(['update:tab'])
// 所有字段
const allFields = shallowRef<LabelFieldConfig[]>([])
// 表格数据
const tableData = ref<QueryConfApi.QueryTableRow[]>([])
const deptList = ref<Tree[]>([])
const showDialog = ref<boolean>(false)
const showSubFieldDialog = ref<boolean>(false)
const showPreviewDialog = ref<boolean>(false)
const currentRow = ref<QueryConfApi.QueryTableRow | null>(null) // 当前行
const selectedRowKeys = ref<QueryConfApi.QueryTableRow[]>([])
const tableRef = ref<InstanceType<typeof ElTable>>()
const sortable = ref(null)
const isLoading = ref(false) // 是否加载中
const subFieldExcludedKeys = computed(() => {
  // 只排除所有已在主表中的字段
  const mainKeys = tableData.value.map((i) => i.uuid).filter((id): id is string => id !== undefined)
  return mainKeys
})

function addFields(ids: string[]) {
  const existKeysSet = new Set(tableData.value.map((i) => i.uuid))
  const toAdd = allFields.value.filter((f) => f.uuid && ids.includes(f.uuid) && !existKeysSet.has(f.uuid))
  toAdd.forEach(async f => {
    const newField = {
      ...f,
      field: [{...f}],
      queryType: fieldTypeLabelMap[f.fieldType],
      defaultValue: '',
      hint: '',
      sort: 0,
    }
    const options = await getEnumOptions(newField)
    tableData.value.push({
      ...newField,
      selectedOptions: options,
    })
  })

}


function removeSelected() {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    isLoading.value = true
    emits('update:tab', true)
    const ids = selectedRowKeys.value.map(r => r.id).filter(Boolean) as string[]
    const uuids = selectedRowKeys.value.map(r => r.uuid).filter(Boolean) as string[]

    const filterTable = () => {
      tableData.value = tableData.value.filter(
        row => !uuids.includes(row.uuid!) && !ids.includes(row.id!)
      )
      selectedRowKeys.value = []
      isLoading.value = false
      emits('update:tab', false)
      ElMessage.success('删除成功')
    }

    if (ids.length > 0) {
      const idsStr = ids.map(value => `ids=${encodeURIComponent(value)}`).join('&')
      QueryConfApi.deleteQueryConfList(`manageId=${query.manageId as string}&${idsStr}`)
        .then(filterTable)
        .catch(() => ElMessage.error('删除失败'))
    } else {
      filterTable()
    }
  })
}

function onSelectionChange(rows: QueryConfApi.QueryTableRow[]) {
  selectedRowKeys.value = rows
}

function openSubFieldDialog(row: QueryConfApi.QueryTableRow) {
  currentRow.value = row
  showSubFieldDialog.value = true
}

function addSubFields(ids: string[]) {
  if (!currentRow.value) return
  const toAdd = allFields.value.filter((f) => ids.includes(f.uuid!))
  currentRow.value.field = toAdd
  showSubFieldDialog.value = false
}

// 初始化 Sortable
const initSortable = () => {
  nextTick(() => {
    const tableEl = (tableRef.value as any)?.$el.querySelector(
      '.query-sortable-table-container .el-table__body-wrapper tbody'
    )

    if (tableEl && !sortable.value) {
      sortable.value = new Sortable(tableEl, {
        animation: 150,
        handle: '.el-table__row',
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',

        onStart: () => {},

        onEnd: (evt) => {
          const { oldIndex, newIndex } = evt
          if (oldIndex !== newIndex) {
            const item = tableData.value.splice(oldIndex, 1)[0]
            tableData.value.splice(newIndex, 0, item)
          }
        }
      })
    }
  })
}

const tableRowClassName = ({
  row,
}: {
  row: any
}) => {
  if (row.queryType === 0) {
    return 'warning-row'
  }
  return ''
}
/** 获得部门树 */
const getTree = async () => {
  const res = await LabelManageApi.getLabelManageTree({
    labelId: query.lableId as string
  })
  deptList.value = handleTree2(res)
}

const fetchData = async () => {
  const res = await FieldConfApi.getFieldConfigListByManageId({
    manageId: query.manageId as string
  })
  const queryConfList = await QueryConfApi.getQueryConfList({
    manageId: query.manageId as string
  })
  allFields.value = res.map(item => {
    item.uuid = item.id
    delete item.id
    return item
  })

  tableData.value = (queryConfList || []).map(item => {
    const field = item.fieldIds ? allFields.value.filter(f => item.fieldIds?.split(',').includes(f.uuid!)) : []
    return {
    ...item,
    fieldType: field[0]?.fieldType,
    // defaultValue: item.queryType === 1 || item.queryType === 2 ? item.defaultValue.split(',') : item.defaultValue,
    field,
  }
  }) as QueryConfApi.QueryTableRow[]

}

// 生命周期钩子
onMounted(() => {
  initSortable()
  fetchData()
  getTree()
})

const submitForm = async () => {
  isLoading.value = true
  emits('update:tab', true)
  const submitData: QueryConfApi.QueryResItem[] = tableData.value.map((row, index) => {
    return {
      fieldIds: row.field?.map(f => f.uuid!).join(',') || '',
      fieldCodes: row.field?.map(f => f.code).join(',') || '',
      queryType: row.queryType,
      sort: index,
      defaultValue:  row.defaultValue,
      hint: row.hint,
      manageId: query.manageId as string,
      id: row.id,
    }
  })
  QueryConfApi.updateQueryConfList(submitData).then(() => {
    ElMessage.success('更新成功')
    fetchData()
  }).catch(() => {
    ElMessage.error('更新失败')
  }).finally(() => {
    isLoading.value = false
    emits('update:tab', false)
  })
}

defineExpose({ submitForm })


const getEnumOptions = async (row) => {
  // 标签和区域
  if(row.fieldType === FieldType.REGION || row.fieldType === FieldType.TAG){
    return deptList.value
  }
  // 单选、多选需要获取字典
  const res = await FieldConfApi.getFieldConfigDetail({ 'id': row.uuid as string })
  const val = res.fieldConfExtDOList?.[0]?.value
  if(val){
    const data = await DictDataApi.getDictDataPage({
      pageNo: 1,
      pageSize: 10,
      dictType: val,
    })

    return data.list
  }
  return []
}
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-table-tr-bg-color);
}
</style>

