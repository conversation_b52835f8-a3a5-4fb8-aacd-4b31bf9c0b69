<template>
  <ContentWrap>
    <el-row :gutter="20" class="mb-4">
      <el-col :span="10">
        <el-alert
          title="仅能删除本次新增字段，保存后字段无法再删除"
          type="info"
          :closable="false"
          show-icon
        />
      </el-col>
      <el-col :span="6" :offset="8">
        <el-button :disabled="isLoading" v-if="showAddBaseButton" @click="openBaseForm">选择基础字段</el-button>
        <el-button :disabled="isLoading" @click="openForm"> {{ isBaseTag ? '添加基础字段' : '添加列表字段' }}</el-button>
        <el-button :disabled="multipleSelection.length !== 1 || isLoading" type="primary" @click="handleEdit"
          >编辑</el-button
        >
        <!-- 长度为1 且 id 为空 才可以删除 -->
        <el-button :disabled="!(multipleSelection.length === 1 && !multipleSelection[0].id) || isLoading" type="success" @click="handleDelete"
          >删除</el-button
        >
      </el-col>
    </el-row>
    <el-table
      v-loading="isLoading"
      ref="tableRef"
      :data="tableData.filter(item => item.parentCode === '0')"
      stripe
      class="field-sortable-table-container"
      row-key="uuid"
       height="640"
      @selection-change="handleSelectionChange"
       :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column type="selection" width="55" :selectable="selectable" />
      <el-table-column prop="code" label="Code" align="center">
        <template #default="scope">
          {{ scope.row.code }}
            <el-tag type="primary" >{{ BusinessTypeLabel[scope.row.bizType] }}</el-tag>
          </template>
        </el-table-column>
      <el-table-column prop="name" label="字段名称" align="center">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleRowClick(scope.row)"
          >
          {{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="字段说明" align="center" />
      <el-table-column prop="fieldType" label="字段类型" align="center" >
        <template #default="scope">
          {{ FieldTypeLabel[scope.row.fieldType] }}
        </template>
      </el-table-column>
      <el-table-column prop="length" label="字段长度" align="center" width="100">
        <template #default="scope">
          {{ scope.row.length }}
        </template>
      </el-table-column>
      <el-table-column prop="encFlag" label="是否加密" align="center" width="100">
        <template #default="scope">
          {{ scope.row.encFlag ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="addFlag" label="新增表单" align="center" width="100">
        <template #default="scope">
          <el-checkbox :disabled="scope.row.bizType === `${BusinessType.SYSTEM}`" v-model="scope.row.addFlag" :true-value="1" :false-value="0" label="" />
        </template>
      </el-table-column>
      <el-table-column prop="editFlag" label="编辑表单" align="center" width="100">
        <template #default="scope">
          <el-checkbox :disabled="scope.row.bizType === `${BusinessType.SYSTEM}`" v-model="scope.row.editFlag" :true-value="1" :false-value="0" label="" />
        </template>
      </el-table-column>
      <el-table-column prop="appViewFlag" label="移动端列表" align="center" width="100">
        <template #default="scope">
          <div class="cursor-pointer" @click="scope.row.bizType === `${BusinessType.SYSTEM}` ? null : handleViewFlag(scope.row, 'appViewFlag')">
            <el-icon v-if="scope.row.appViewFlag"><View /></el-icon>
            <el-icon v-else><Hide /></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="pcViewFlag" label="管理端列表" align="center"  width="100">
        <template #default="scope">
          <div class="cursor-pointer" @click="scope.row.bizType === `${BusinessType.SYSTEM}` ? null : handleViewFlag(scope.row, 'pcViewFlag')">
            <el-icon v-if="scope.row.pcViewFlag"><View /></el-icon>
            <el-icon v-else><Hide /></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="排序" width="140" align="center">
        <template #default="">
          <Icon icon="ep:rank" class="text-red-500 mr-2 cursor-pointer" />
        </template>
      </el-table-column>
    </el-table>
    <FieldEdit ref="formRef" @update:data="updateData" />
    <FieldAdd ref="baseFormRef" @update:data="updateData" />
  </ContentWrap>
</template>

<script setup lang="ts">
import Sortable from 'sortablejs'
import { View, Hide } from '@element-plus/icons-vue'
import type { TableInstance } from 'element-plus'
import * as LabelApi from '@/api/system/label'
import * as FieldConfApi from '@/api/system/data/field-conf'
import { generateUUID } from '@/utils'
import FieldEdit from './FieldEdit.vue'
import FieldAdd from './FieldAdd.vue'
import { FieldTypeLabel, FieldType } from '@/config/constants/enums/field'
import {
  BooleanEnum,
  BusinessType,
  BusinessTypeLabel,
} from '@/config/constants/enums/label'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'
import { omit } from 'lodash-es'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
const { query } = useRoute() // 查询参数
const message = useMessage() // 消息弹窗
const emits = defineEmits(['update:tab'])

const tableRef = ref<TableInstance | null>(null)
const formRef = ref()
const baseFormRef = ref()
const multipleSelection = ref<LabelFieldConfig[]>([])
const sortable = ref(null)
const tableData = ref<LabelFieldConfig[]>([])
const selectable = (row: LabelFieldConfig) => ![BusinessType.SYSTEM].includes(row.bizType) // 系统标签不能选中
const isLoading = ref(false) // 是否加载中
const showAddBaseButton = computed(() => !(query.rootId == null || Number(query.rootId) === 0)); // 是否显示选择基础字段按钮
const isBaseTag = computed(() => String(query.type) === String(BooleanEnum.FALSE)); // 是否是基础标签

// 初始化 Sortable
const initSortable = () => {
  nextTick(() => {
    const tableEl = (tableRef.value as any)?.$el.querySelector(
      '.field-sortable-table-container .el-table__body-wrapper tbody'
    )

    if (tableEl && !sortable.value) {
      sortable.value = new Sortable(tableEl, {
        animation: 150,
        handle: '.el-table__row',
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',

        // 开始拖拽
        onStart: () => {},

        // 结束拖拽
        onEnd: (evt) => {
          const { oldIndex, newIndex } = evt
          if (oldIndex !== newIndex) {
            // 调整表格数据顺
            const item = tableData.value.splice(oldIndex, 1)[0]
            tableData.value.splice(newIndex, 0, item)
          }
        }
      })
    }
  })
}

const handleSelectionChange = (val: LabelFieldConfig[]) => {
  multipleSelection.value = val
}

const handleRowClick = (row: LabelFieldConfig) => {
  formRef.value.open('show', row, tableData.value)
}

const openBaseForm = () => {
  baseFormRef.value.open(unref(tableData.value.filter(item => item.parentCode === '0')) )
}

const getDataFieldConfListByManageId = async () => {
  isLoading.value = true
  try {
    const res = await FieldConfApi.getFieldConfigList({
      manageId: query.manageId as string
    })
    tableData.value = (res || []).map(item => {
    return {
      ...item,
      uuid: item.id ? item.id : generateUUID()
    }
  })
  } catch (error) {
    message.error('获取数据失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 监听数据变化
watch(
  () => props.data,
  (newVal) => {
    tableData.value = [...(newVal as LabelFieldConfig[])]
    if (tableRef.value) {
      initSortable()
    }
  },
  { deep: true }
)

// 事件处理
const handleEdit = () => {
  // 多选、单选先删掉附属属性
  const {code, fieldType} = multipleSelection.value[0]
  if (fieldType === FieldType.RADIO || fieldType === FieldType.CHECKBOX) {
    tableData.value = tableData.value.filter(item => item.parentCode !== code+ '_parent_code')
  }
  formRef.value.open('edit', multipleSelection.value[0], tableData.value)
}

// 事件处理
const handleDelete = () => {
  const { id, uuid, code } = multipleSelection.value[0]
  // 多选、单选删掉附属属性, 并删除掉当前项
  // todo zhaokun 新保存未调接口的都会被删除
  tableData.value = tableData.value.filter((item) =>
    item?.id !== id &&
    item.uuid !== uuid &&
    item.parentCode !== code + '_parent_code'
  )
}

const updateData = (data: LabelFieldConfig | LabelFieldConfig[], callback?: () => void) => {
   if (Array.isArray(data)) {
    data.forEach(item => updateData(item));
    callback?.()
    return;
  }
  if (!data.uuid && !data.id) {
    data.uuid = generateUUID();
  }
  const identifier = data.id || data.uuid;
  const index = tableData.value.findIndex(
    item => item.id === identifier || item.uuid === identifier
  );
  // 如果 data 没有 uuid 则生成一个
  if (index !== -1) {
    // 使用展开运算符保留响应式
    tableData.value.splice(index, 1, { ...tableData.value[index], ...data });
  } else {
    tableData.value.push({ ...data });
  }
  callback?.()
}

const handleViewFlag = (row: LabelFieldConfig, flag: string) => {
  row[flag] = row[flag] === BooleanEnum.TRUE ? BooleanEnum.FALSE : BooleanEnum.TRUE
}


const saveTableData = async () => {
  const data = tableData.value.map((item, index) => omit({
    ...item,
    sort: index + 1
  }, ['uuid']))
  handleSelectionChange([])
  isLoading.value = true
  await FieldConfApi.updateFieldConfigList(data).then(() => {
    message.success('保存成功')
    getDataFieldConfListByManageId()
  }).catch(() => {
    message.error('保存失败，请稍后重试')
  }).finally(() => {
    isLoading.value = false
    emits('update:tab', false)
  })
}

/** 添加/修改操作 */
const openForm = () => {
  multipleSelection.value = []
  formRef.value.open('add', undefined, tableData.value)
}
// 生命周期钩子
onMounted(() => {
  initSortable()
  getDataFieldConfListByManageId()
})

onBeforeUnmount(() => {
  // 销毁 Sortable 实例
  if (sortable.value) {
    ;(sortable.value as any).destroy()
    sortable.value = null
  }
})
defineExpose({ saveTableData })
</script>

<style scoped>
/* 拖拽时的样式 */
.sortable-ghost {
  background-color: #f5f7fa;
  opacity: 0.6;
}

.sortable-chosen {
  background-color: #e6f7ff;
}

.sortable-drag {
  cursor: move;
}
</style>
