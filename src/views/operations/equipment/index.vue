<template>
  <div class="mr-8">
    <ContentWrap class="my-component">
      <div class="top-title">运维设备列表</div>
      <div class="top-equipment">
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/camera.png" alt="" />
            </div>
            <div class="right-txt">
              <div>设备总数</div>
              <div>{{ equipmentBasis.deviceTotal }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/camera2Big.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/time.png" alt="" />
            </div>
            <div class="right-txt">
              <div>设备完好率</div>
              <div
                >{{
                  (equipmentBasis.rate == null ? 0 : parseFloat(equipmentBasis.rate) || 0).toFixed(
                    2
                  )
                }}%</div
              >
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/time2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/normal.png" alt="" />
            </div>
            <div class="right-txt">
              <div>正常设备</div>
              <div>{{ equipmentBasis.normal }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/normal2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/label.png" alt="" />
            </div>
            <div class="right-txt">
              <div>报备设备</div>
              <div>{{ equipmentBasis.report }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/label2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/fault.png" alt="" />
            </div>
            <div class="right-txt">
              <div>故障设备</div>
              <div>{{ equipmentBasis.malfunction }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/fault2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/warning.png" alt="" />
            </div>
            <div class="right-txt">
              <div>告警设备</div>
              <div class="fs-w">{{ WarningCount.noDealCount }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/warning2.png" alt="" />
          </div>
        </div>
      </div>
      <div class="main">
        <el-row :gutter="20">
          <el-col :span="4" :xs="24">
            <div class="left-year">
              <DeptTree @node-click="handleDeptNodeClick" />
            </div>
          </el-col>
          <el-col :span="20" :xs="24">
            <div class="right-main">
              <!-- 表单部分 -->
              <ContentWrap class="form-box">
                <el-form
                  ref="queryFormRef"
                  :inline="true"
                  :model="form"
                  class="form-search flex flex-wrap items-start -mb-15px"
                >
                  <el-form-item label="" class="!mr-3">
                    <el-input
                      v-model="form.assets"
                      class="!w-240px"
                      clearable
                      placeholder="输入设备编号/名称"
                    />
                  </el-form-item>
                  <el-form-item label="设备类型" class="!mr-3">
                    <el-cascader
                      v-model="form.assetsTypeId"
                      :options="typeList"
                      :props="cascaderProps"
                      collapse-tags
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="区域" prop="areaId" class="!mr-3">
                    <Dictionary
                      v-model="form.areaId"
                      type="cascader"
                      width="240px"
                      dict-type="oamArea"
                      :cascader-props="{
                        multiple: true,
                        checkStrictly: true,
                        label: 'name',
                        value: 'id'
                      }"
                      :max-collapse-tags="2"
                      placeholder="请选择区域"
                    />
                  </el-form-item>
                  <el-form-item label="设备状态" class="!mr-3">
                    <el-select
                      v-model="form.deviceStatus"
                      class="!w-140px"
                      clearable
                      placeholder="选择设备状态"
                    >
                      <el-option label="正常" :value="0" />
                      <el-option label="故障" :value="1" />
                      <el-option label="报备" :value="2" />
                      <el-option label="告警" :value="3" />
                    </el-select>
                  </el-form-item>

                  <div class="min-w-[850px] flex-1 flex justify-between mb-[18px] pl-2">
                    <div>
                      <XButton
                        title="查询"
                        preIcon="ep:search"
                        gradient
                        @click="onSubmit"
                        v-hasPermi="['infra:device-overview-info:query']"
                      />
                      <el-button @click="resetForm(true)"><Icon icon="ep:refresh" />重置</el-button>
                      <XButton
                        class="text-button"
                        preIcon="ep:search"
                        title="高级搜索"
                        width="100px"
                        gradient
                        @click="showAdvancedSearch"
                        v-hasPermi="['infra:device-overview-info:query']"
                      />
                    </div>
                    <div>
                      <el-button
                        @click="synchronousClick"
                        v-hasPermi="['infra:device-overview-info:update']"
                        ><Icon icon="ep:switch" />同步</el-button
                      >
                      <el-button @click="initiatedTickets"
                        ><Icon icon="ep:tickets" />发起工单</el-button
                      >
                      <el-button
                        plain
                        @click="addFun"
                        v-hasPermi="['infra:device-overview-info:create']"
                      >
                        <Icon icon="ep:plus" />
                        新增
                      </el-button>
                      <el-button
                        plain
                        @click="importClick"
                        v-hasPermi="['infra:device-overview-info:import']"
                      >
                        <Icon icon="ep:upload" /> 导入
                      </el-button>
                      <el-button
                        plain
                        @click="handleExport"
                        :loading="exportLoading"
                        v-hasPermi="['infra:device-overview-info:export']"
                      >
                        <Icon icon="ep:download" />导出
                      </el-button>
                    </div>
                  </div>
                </el-form>
              </ContentWrap>
              <!-- 表格部分 -->
              <div class="right-table">
                <el-table
                  :data="tableData"
                  height="530"
                  v-loading="loading"
                  style="width: 100%; margin-bottom: 10px"
                  @selection-change="handleSelectionChange"
                  :header-cell-style="{
                    'background-color': '#F4F6F8',
                    'font-size': '14px',
                    color: '#3B3C3D'
                  }"
                >
                  <!-- 多选框列 -->
                  <el-table-column type="selection" :selectable="selectable" min-width="55" />
                  <!-- 其他列 -->
                  <el-table-column type="index" :index="tableIndex" label="序号" min-width="60" />
                  <el-table-column
                    prop="assetsCode"
                    label="设备编号"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="assetsName"
                    label="设备名称"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="assetsTypeName"
                    label="设备类型"
                    min-
                    width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="area"
                    label="所属区域"
                    min-width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="projectName"
                    label="所属项目"
                    min-width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="operatingUnitName"
                    label="运营商"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="maintenanceUnitName"
                    label="维保单位"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column prop="ip" label="ip" min-width="100" show-overflow-tooltip />
                  <el-table-column
                    prop="assetsSourceName"
                    label="设备来源"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="label"
                    label="标签"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="warnName"
                    label="告警方式"
                    min-width="90"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-tooltip
                        :content="scope.row.warnTypeDescription"
                        placement="top"
                        v-if="scope?.row?.warnName"
                      >
                        <span @mouseenter="loadWarnTypeDescription(scope.row)">
                          {{ scope.row.warnName }}
                          <img src="@/assets/imgs/icons/faultTab2.png" alt="" />
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="deviceStatus" label="设备状态" min-width="90">
                    <template #default="scope">
                      <div :class="UTILS.getStatusClass(scope.row.deviceStatus)">
                        {{ UTILS.getStatusText(scope.row.deviceStatus) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="syncStatus" label="同步状态" min-width="90">
                    <template #default="scope">
                      <div :class="UTILS.getStatusClass2(scope.row.syncStatus)">
                        {{ UTILS.getStatusText2(scope.row.syncStatus) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="date" label="操作" min-width="140" fixed="right">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="handleClick(scope.row.id)"
                        v-hasPermi="['infra:device-overview-info:query']"
                        >详情</el-button
                      >
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="goEdit(scope.row.id)"
                        v-hasPermi="['infra:device-overview-info:update']"
                        >编辑</el-button
                      >
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="deleteClick(scope.row.id)"
                        v-hasPermi="['infra:device-overview-info:delete']"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页部分 -->
                <div class="demo-pagination-block">
                  <el-pagination
                    v-model:current-page="form.pageNo"
                    v-model:page-size="form.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100]"
                    :size="size"
                    :disabled="disabled"
                    :background="background"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 右侧盒子 -->
      </div>

      <!-- 高级搜索遮罩层 -->
      <el-dialog
        v-model="showDialog"
        title="高级搜索"
        width="45%"
        @close="closeAdvancedSearchDialog"
      >
        <el-form :model="form" class="form-search">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="设备编号:">
                <el-input v-model="form.assetsCode" placeholder="输入设备编号" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="设备名称:">
                <el-input v-model="form.assetsName" placeholder="输入设备名称" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="设备类型:" v-hasPermi="['infra:device-type:list']">
                <el-cascader
                  style="width: 100%"
                  v-model="form.assetsTypeId"
                  :options="typeList"
                  :props="cascaderProps"
                  collapse-tags
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="所属区域:"
                prop="areaId"
                v-hasPermi="['infra:device-dict:DictTypeList']"
              >
                <Dictionary
                  style="width: 100%"
                  v-model="form.areaId"
                  type="cascader"
                  width="240px"
                  dict-type="oamArea"
                  :cascader-props="{
                    multiple: true,
                    checkStrictly: true,
                    label: 'name',
                    value: 'id'
                  }"
                  :max-collapse-tags="2"
                  placeholder="请选择区域"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="运营商:">
                <el-select v-model="form.operatingUnit" placeholder="选择运营商">
                  <el-option
                    v-for="item in operatingUnitList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="维保单位:">
                <el-select v-model="form.maintenanceUnit" placeholder="选择维保单位">
                  <el-option
                    v-for="item in maintenanceUnitList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="设备来源:" v-hasPermi="['infra:device-dict:DictTypeList']">
                <el-select v-model="form.assetsSource" placeholder="选择设备来源">
                  <el-option
                    v-for="item in assetsSourceList"
                    :key="item.id"
                    :label="item.value"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="告警方式:">
                <el-select v-model="form.warnId" placeholder="选择告警方式">
                  <el-option
                    v-for="item in warnIdList"
                    :key="item.id"
                    :label="item.warnTypeName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="同步状态:">
                <el-select v-model="form.syncStatus" placeholder="选择同步状态">
                  <el-option label="未同步" :value="0" />
                  <el-option label="已同步" :value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" class="bottom-btn">
              <el-button type="primary" @click="submitAdvancedSearch">确认</el-button>
              <el-button @click="resetForm(true)">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>
    </ContentWrap>
  </div>

  <ImportSingle ref="importSingleRef" @success="getList" :project-id="ProjectId" />
</template>

<script lang="ts" setup>
import { hasPermission } from '@/directives/permission/hasPermi'
import * as WorkApi from '@/api/work'

import ImportSingle from './components/ImportSingle.vue'
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import * as ListApi from '@/api/operations/list'
import * as EquipmentApi from '@/api/operations/equipment'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import { getDictList } from '@/api/infra/deviceDict' //设备来源
import * as DynamicFormApi from '@/api/DynamicForm'
import { useRouter, useRoute } from 'vue-router'
import DeptTree from './components/DeptTree.vue'
import download from '@/utils/download'
import * as UTILS from './utils'
import { getWarnTypeDescription } from '../list/utils/index'
import { ElMessage } from 'element-plus'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()
//序号自增
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}
const getOperatingUnit = async (type: string) => {
  const unitTypeData = await DynamicFormApi.getUnitType({ code: type })
  const unitData = await DynamicFormApi.getUnit({ typeId: unitTypeData })
  return unitData
}

// 统计设备
const equipmentBasis = ref({
  deviceTotal: 0,
  rate: '0',
  normal: 0,
  malfunction: 0,
  report: 0,
  deviceType: [
    {
      id: 0,
      name: '',
      deviceTotal: 0,
      rate: 0
    }
  ],
  project: [
    {
      id: 0,
      name: '',
      deviceTotal: 0,
      rate: 0
    }
  ],
  maintenanceUnit: [
    {
      id: 0,
      name: '',
      deviceTotal: 0,
      rate: 0
    }
  ],
  area: [
    {
      id: 0,
      name: '',
      deviceTotal: 0,
      rate: 0
    }
  ]
})
// const getEquipmentBasisFun = async () => {
//   const data = await WorkApi.getEquipmentBasis()
//   equipmentBasis.value = data
//   // console.log('打印设备数量', equipmentBasis.value)
// }
// 告警数量
const WarningCount = ref({
  allCount: 0,
  dealCount: 0,
  dealingCount: 0,
  noDealCount: 0,
  sourceCount: {}, //将key和val分别取出来
  todayCount: 0
})
// const getWarningCountFun = async () => {
//   const data = await WorkApi.getWarningCount()
//   WarningCount.value = data
//   // console.log('父组件打印告警数量', data)
// }
onMounted(() => {
  getType()
  getProject() //获得所属项目
  getOperatingUnitFun() //获得运营商
  getMaintenanceUnitFun() //获得维保单位
  getAssetsSourceList() //获得设备来源
  getWarnIdList() //获得告警方式
  // getList() 取消默认的获取数据防止二次调用闪屏刷新
  window.addEventListener('keydown', handleGlobalKeyDown)
  // 统计设备基础
  // if (hasPermission(['infra:device-overview-info:query'])) {
  //   getEquipmentBasisFun()
  // }
  // 告警数量
  // if (hasPermission(['infra:warn-assets:query'])) {
  //   getWarningCountFun()
  // }
  // console.log('判断成立吗1', hasPermission(['infra:warn-assets:query']))
  // console.log('判断成立吗2', hasPermission(['infra:666']))

  const deviceStatus = route.query.deviceStatus ? route.query.deviceStatus : ''
  // console.log('打印设备状态', deviceStatus)

  if (deviceStatus !== undefined && deviceStatus !== '') {
    // 如果数据存在则进行赋值查询
    form.deviceStatus = Number(deviceStatus)
    // 调用查询方法
    onSubmit()
  }
})
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})
const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}
//顶部两个下拉
const cascaderProps = reactive({
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})
// 高级搜索项目列表树状下拉
const cascaderProps2 = reactive({
  value: 'id',
  label: 'projectName',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})
const typeList = ref([])
const getType = async () => {
  const data = await TicketsPushApi.getType()
  // console.log('设备类型typeList', data)
  typeList.value = data
}
const projectList = ref([]) //所属项目
const getProject = async () => {
  const data = await DynamicFormApi.getBelongsProject()
  // console.log('所属项目', data)
  projectList.value = data
}

const operatingUnitList = ref([]) //运营商/运营单位
const getOperatingUnitFun = async () => {
  const data = await getOperatingUnit('operation')
  // console.log('运营商/运维单位', data)
  operatingUnitList.value = data
}
const maintenanceUnitList = ref([]) //维保单位
const getMaintenanceUnitFun = async () => {
  const data = await getOperatingUnit('maintenance')
  // console.log('维保单位', data)
  maintenanceUnitList.value = data
}
const assetsSourceList = ref([]) //设备来源
const getAssetsSourceList = async () => {
  const data = await getDictList('deviceSource')
  // console.log('设备来源', data)
  assetsSourceList.value = data
}
const warnIdList = ref([]) // 告警方式
const getWarnIdList = async () => {
  const data = await DynamicFormApi.getWarning()
  // console.log('告警方式', data)
  warnIdList.value = data
}
// 缓存已请求过的 warnType
const warnTypeCache = new Map()
const loadWarnTypeDescription = async (row) => {
  if (!row.warnTypeDescription && row.warnId) {
    if (warnTypeCache.has(row.warnId)) {
      row.warnTypeDescription = warnTypeCache.get(row.warnId)
    } else {
      try {
        const description = await getWarnTypeDescription(row.warnId)
        row.warnTypeDescription = description
        warnTypeCache.set(row.warnId, description)
      } catch (error) {
        // console.error('获取告警方式描述失败:', error)
        row.warnTypeDescription = '获取失败'
      }
    }
  }
}

const getList = async () => {
  loading.value = true
  // 搜索接口
  try {
    const data = await EquipmentApi.getList(form)
    // 初始化时不请求描述，只赋空值
    data.list.forEach((item) => {
      item.warnTypeDescription = null
    })
    // console.log('data.list', data.list)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
  // 设备接口 获取设备详细信息
  try {
    const deviceObj = await EquipmentApi.postOverview(form)
    equipmentBasis.value = deviceObj
    // console.log('equipmentBasis.value', equipmentBasis.value)
  } finally {
    loading.value = false
  }
  // 告警接口 获取告警详细信息
  try {
    const deviceObj = await EquipmentApi.getWarn(form)
    WarningCount.value = deviceObj
    // console.log('WarningCount.value', WarningCount.value)
  } finally {
    loading.value = false
  }
}

// 表格数据
const tableData = ref([])

// 搜索表单数据
const form = reactive({
  assets: null,
  assetsCode: null, //设备编号
  assetsTypeId: [], //设备类型
  areaId: [], //所属区域
  deviceStatus: null, //设备状态
  pageNo: 1,
  pageSize: 10,
  // ====
  assetsName: null, //设备名称++
  projectId: [], //所属项目++
  operatingUnit: null, //运营商
  maintenanceUnit: null, //推送单位/维保单位
  assetsSource: null, //设备来源
  warnId: null, //告警方式
  syncStatus: null, //同步状态(0 未同步，1 已同步)
  ids: [] //多选框选中的id 批量导出使用
})

// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

// 多选框相关
const selectedRows = ref([]) // 选中的行数据

// 处理选中行变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
  // 有选中的进行赋值用于导出使用
  if (selectedRows.value.length) {
    form.ids = rows.map((item) => item.id)
  } else {
    form.ids = [] //没有选中的置空
  }
  // console.log('选中的行数据：', selectedRows.value)
}

// 控制哪些行可以被选择
const selectable = (row, index) => {
  // 例如：禁用第一行的选择
  return index !== -1
}

// 其他事件
const handleSizeChange = (val: number) => {
  form.pageSize = val
  getList() //重新获取
}
const handleCurrentChange = (val: number) => {
  form.pageNo = val
  getList() //重新获取
}

const handleClick = (id: number) => {
  sessionStorage.removeItem('equipmentDetailCode') //传id清除code 传code清除id
  sessionStorage.setItem('equipmentDetailId', id.toString())
  router.push({
    path: '/operations/equipmentDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}

const onSubmit = () => {
  form.ids = [] //清除多选框选中后在搜索，防止只搜多选框
  getList()
}

const resetForm = (flag: boolean) => {
  form.assets = null
  form.assetsCode = null // 设备编号
  form.assetsTypeId = [] // 设备类型
  form.areaId = [] // 所属区域
  form.deviceStatus = null //设备状态
  form.pageNo = 1 // 分页页码
  form.pageSize = 10 // 分页大小
  // form.deptId = null // 部门ID
  form.assetsName = null // 设备名称
  form.operatingUnit = null // 运营商
  form.maintenanceUnit = null // 推送单位/维保单位
  form.assetsSource = null // 设备来源
  form.warnId = null // 告警方式
  form.syncStatus = null // 同步状态
  form.ids = [] //选中的数据
  // 使用接受的flag来判断是否需要重新请求数据 高级搜索弹窗关闭部分不需要
  if (flag) {
    getList() // 调用获取列表的函数
  }
}
const closeAdvancedSearchDialog = () => {
  showDialog.value = false
  // 不调用 resetForm 或者在 resetForm 中不重置数据
}

// 处理项目被点击
const handleDeptNodeClick = async (row: { [key: string]: any }) => {
  form.projectId = [] // 清空旧数据

  if (row.children && row.children.length > 0) {
    // 如果是父节点，收集所有子节点的 id
    form.projectId = row.children.map((child) => child.id)
  } else {
    // 如果是子节点，直接使用当前 id
    form.projectId.push(row.id)
  }

  getList() // 触发查询
}
// const handleDeptNodeClick = async (row: { [key: string]: any }) => {
//   form.projectId = [] //先清空在添加
//   // console.log('父组件接收到的参数', row)
//   form.projectId.push(row.id)
//   getList()
// }

// 点击新增跳转
const addFun = () => {
  const equipmentDetail = {
    title: '运维设备列表-新增'
  }
  sessionStorage.setItem('equipmentDetail', JSON.stringify(equipmentDetail))
  router.push({
    path: '/operations/equipmentAdd'
  })
}

// 点击编辑跳转
const goEdit = (id: number) => {
  const equipmentDetail = {
    id: id.toString(),
    title: '运维设备列表-编辑'
  }
  sessionStorage.setItem('equipmentDetail', JSON.stringify(equipmentDetail))
  router.push({
    path: '/operations/equipmentAdd'
  })
}
// 点击删除
const deleteClick = async (id: number) => {
  // 执行删除操作
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await EquipmentApi.deleteList({ id })
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
// 同步
const synchronousClick = async () => {
  if (selectedRows.value.length) {
    const deviceCode = selectedRows.value.map((row) => row.id)
    // console.log('打印id数组', deviceCode)
    try {
      await EquipmentApi.synchronous(deviceCode)
      ElMessage.success('同步成功')
    } catch (error) {
      ElMessage.error('同步失败')
    }
  } else {
    ElMessage.warning('请选中要同步的设备')
    return
  }
}
// 发起工单
const initiatedTickets = async () => {
  ElMessage.warning('暂未开发')
}

// 高级搜索相关
const showDialog = ref(false)
const showAdvancedSearch = () => {
  showDialog.value = true
  // 每次打开遮罩层时重置表单数据
  // resetForm(true)
}

const submitAdvancedSearch = async () => {
  // console.log('高级搜索表单数据:', form)
  form.ids = [] //清除多选框选中后在搜索，防止只搜多选框
  await getList()
  showDialog.value = false
}

/** 导入按钮操作*/
const importSingleRef = ref()
const showImportSingle = ref(false)
const ProjectId = ref(0)
const importClick = async () => {
  ProjectId.value = form.projectId[0]
  // console.log('导入的id', ProjectId.value)
  showImportSingle.value = true
  importSingleRef.value.open()
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await EquipmentApi.exportList(form)
    download.excel(data, '用户数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
// 防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function (...args) {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100% !important;
  // height: 100% !important;
  background-color: white;
  display: flex;
  flex-direction: column;
  .top-title {
    width: 100%;
    height: 60px;
    // background-color: #409eff;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  .top-equipment {
    // 隐藏滚动条
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Edge */
    }
    width: 100%;
    height: 70px;
    // overflow-x: auto;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 10px; // 设置固定间距，例如 10px
    // overflow-x: auto;
    // padding: 0 20px;
    .top-equipment-main {
      // width: 200px;
      // height: 70px;
      flex: 1;
      background-color: #f7f8f8;
      display: flex;
      justify-content: space-between;
      .top-equipment-box {
        width: 130px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .left-img {
          width: 40px;
          height: 40px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .right-txt {
          width: 50%;
          height: 100%;
          font-size: 13px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          :nth-child(1) {
            text-align: center;
          }
          :nth-child(2) {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
          }
        }
      }
      .grey-img {
        width: 64px;
        height: 64px;
        img {
          width: 64px;
          height: 64px;
        }
      }
    }
  }
  .main {
    // flex: 1;
    // display: flex;//去掉自身样式防止影响布局
    // padding-left: 20px;
    .left-year {
      // width: 200px;
      // height: 600px;
      height: 100%;
      background-color: #f1f3f6;
      overflow-y: auto;
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      // margin-right: 10px;
    }

    // .right-main {
    //   flex: 1;
    //   display: flex;
    //   flex-direction: column;
    //   // overflow-x: auto;
    //   min-width: 0; /* 允许内容被裁剪而不是挤压其他元素 */
    //   // 隐藏滚动条
    //   scrollbar-width: none; /* Firefox */
    //   -ms-overflow-style: none; /* IE 10+ */
    //   &::-webkit-scrollbar {
    //     display: none; /* Chrome, Safari, Edge */
    //   }
    //   .form-box {
    //     width: 100%;
    //     display: flex;
    //     flex-direction: row;
    //     align-items: center;
    //     margin-bottom: 10px;
    //     overflow-x: auto;
    //     .form-search {
    //       flex: 1;
    //       display: flex;
    //       flex-direction: row;
    //       align-items: center;

    //       .el-form-item {
    //         margin-right: 10px;
    //       }
    //     }
    //   }

    //   // .right-table {
    //   //   width: 100%;
    //   //   flex: 1;
    //   //   padding-right: 1px; //去除黑线 操作栏没有贴满右边 导致展示部分文字形成黑线
    //   //   max-height: 540px; //设置最大高度溢出滚动
    //   //   // overflow-y: auto;
    //   //   // 隐藏滚动条
    //   //   scrollbar-width: none; /* Firefox */
    //   //   -ms-overflow-style: none; /* IE 10+ */
    //   //   &::-webkit-scrollbar {
    //   //     display: none; /* Chrome, Safari, Edge */
    //   //   }
    //   //   :deep(.el-table__header th) {
    //   //     text-align: left;
    //   //   }
    //   //   :deep(.el-table td) {
    //   //     text-align: left;
    //   //   }
    //   //   .demo-pagination-block {
    //   //     margin-top: 10px;
    //   //   }
    //   // }
    // }
    .demo-pagination-block {
      display: flex;
      justify-content: flex-end;
    }
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}
// 高级搜索底部按钮
.bottom-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  button {
    width: 100px;
    margin-right: 20px;
  }
}

// 状态文字颜色
.status-normal {
  color: green;
}

.status-alarm {
  color: orange;
}

.status-fault {
  color: red;
}

.status-reporting {
  color: #409eff;
}
</style>
