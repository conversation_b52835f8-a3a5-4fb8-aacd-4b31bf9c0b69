<template>
  <div class="left-bottom-tabs">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="运维项目" name="first">
        <div class="main">
          <div class="title-box p-x-2">
            <span>项目总数:</span>
            <span>{{ props.projectCount.oamCount }}</span>
          </div>
          <div class="demo-progress">
            <!-- 在保 -->
            <div class="line">
              <p class="content">
                <span>在保:</span>
                <span>{{ props.projectCount.oamSafeguardCount }}</span>
              </p>
              <el-popover
                placement="right"
                trigger="hover"
                :show-arrow="false"
                popper-style="padding: 0; border: none; background: transparent;"
              >
                <template #reference>
                  <div class="progress-wrapper">
                    <el-progress
                      :text-inside="true"
                      :stroke-width="ProgressHeight"
                      :percentage="oamRatio1"
                      color="#0FC983"
                      :show-text="oamRatio1 > 0"
                      :class="
                        getProgressClass(oamRatio1) === 'custom-progress'
                          ? 'custom-progress'
                          : [getProgressClass(oamRatio1), 'color1']
                      "
                    />
                  </div>
                </template>
                <template #default>
                  <div class="custom-tooltip">
                    <div class="tooltip-item">
                      <div class="tooltip-header">
                        <span class="color-dot" :style="{ backgroundColor: '#0fc983' }"></span>
                        <span class="label">在保：</span>
                      </div>
                      <div class="tooltip-footer">
                        <span>数量: {{ props.projectCount.oamSafeguardCount }}</span>
                        <span class="percentage" :style="{ color: '#0fc983' }"
                          >{{ oamRatio1 }}%</span
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </el-popover>
            </div>

            <!-- 临近过保 -->
            <div class="line">
              <p class="content">
                <span>临近过保:</span>
                <span>{{ props.projectCount.oamNearNoSafeguardCount }}</span>
              </p>
              <el-popover
                placement="right"
                trigger="hover"
                :show-arrow="false"
                popper-style="padding: 0; border: none; background: transparent;"
              >
                <template #reference>
                  <div class="progress-wrapper">
                    <el-progress
                      :text-inside="true"
                      :stroke-width="ProgressHeight"
                      :percentage="oamRatio2"
                      color="#FF761A"
                      :show-text="oamRatio2 > 0"
                      :class="
                        getProgressClass(oamRatio2) === 'custom-progress'
                          ? 'custom-progress'
                          : [getProgressClass(oamRatio2), 'color2']
                      "
                    />
                  </div>
                </template>
                <template #default>
                  <div class="custom-tooltip">
                    <div class="tooltip-item">
                      <div class="tooltip-header">
                        <span class="color-dot" :style="{ backgroundColor: '#FF761A' }"></span>
                        <span class="label">临近过保：</span>
                      </div>
                      <div class="tooltip-footer">
                        <span>数量: {{ props.projectCount.oamNearNoSafeguardCount }}</span>
                        <span class="percentage" :style="{ color: '#FF761A' }"
                          >{{ oamRatio2 }}%</span
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </el-popover>
            </div>

            <!-- 已过保 -->
            <div class="line">
              <p class="content">
                <span>已过保:</span>
                <span>{{ props.projectCount.oamNoSafeguardCount }}</span>
              </p>
              <el-popover
                placement="right"
                trigger="hover"
                :show-arrow="false"
                popper-style="padding: 0; border: none; background: transparent;"
              >
                <template #reference>
                  <div class="progress-wrapper">
                    <el-progress
                      :text-inside="true"
                      :stroke-width="ProgressHeight"
                      :percentage="oamRatio3"
                      color="#FF3B48"
                      :show-text="oamRatio3 > 0"
                      :class="
                        getProgressClass(oamRatio3) === 'custom-progress'
                          ? 'custom-progress'
                          : [getProgressClass(oamRatio3), 'color3']
                      "
                    />
                  </div>
                </template>
                <template #default>
                  <div class="custom-tooltip">
                    <div class="tooltip-item">
                      <div class="tooltip-header">
                        <span class="color-dot" :style="{ backgroundColor: '#FF3B48' }"></span>
                        <span class="label">已过保：</span>
                      </div>
                      <div class="tooltip-footer">
                        <span>数量: {{ props.projectCount.oamNoSafeguardCount }}</span>
                        <span class="percentage" :style="{ color: '#FF3B48' }"
                          >{{ oamRatio3 }}%</span
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </el-popover>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="建设项目" name="second">
        <div class="main">
          <div class="title-box">
            <span>项目总数:</span>
            <span>{{ props.projectCount.buildCount }}</span>
          </div>
          <div class="demo-progress">
            <!-- 在保 -->
            <div class="line">
              <p class="content">
                <span>在保:</span>
                <span>{{ props.projectCount.buildSafeguardCount }}</span>
              </p>
              <el-popover
                placement="right"
                trigger="hover"
                :show-arrow="false"
                popper-style="padding: 0; border: none; background: transparent;"
              >
                <template #reference>
                  <div class="progress-wrapper">
                    <el-progress
                      :text-inside="true"
                      :stroke-width="ProgressHeight"
                      :percentage="buildRatio1"
                      color="#0FC983"
                      :show-text="buildRatio1 > 0"
                      :class="
                        getProgressClass(buildRatio1) === 'custom-progress'
                          ? 'custom-progress'
                          : [getProgressClass(buildRatio1), 'color1']
                      "
                    />
                  </div>
                </template>
                <template #default>
                  <div class="custom-tooltip">
                    <div class="tooltip-item">
                      <div class="tooltip-header">
                        <span class="color-dot" :style="{ backgroundColor: '#0FC983' }"></span>
                        <span class="label">在保：</span>
                      </div>
                      <div class="tooltip-footer">
                        <span>数量: {{ props.projectCount.buildSafeguardCount }}</span>
                        <span class="percentage" :style="{ color: '#0FC983' }"
                          >{{ buildRatio1 }}%</span
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </el-popover>
            </div>

            <!-- 临近过保 -->
            <div class="line">
              <p class="content">
                <span>临近过保:</span>
                <span>{{ props.projectCount.buildNearNoSafeguardCount }}</span>
              </p>
              <el-popover
                placement="right"
                trigger="hover"
                :show-arrow="false"
                popper-style="padding: 0; border: none; background: transparent;"
              >
                <template #reference>
                  <div class="progress-wrapper">
                    <el-progress
                      :text-inside="true"
                      :stroke-width="ProgressHeight"
                      :percentage="buildRatio2"
                      color="#FF761A"
                      :show-text="buildRatio2 > 0"
                      :class="
                        getProgressClass(buildRatio2) === 'custom-progress'
                          ? 'custom-progress'
                          : [getProgressClass(buildRatio2), 'color2']
                      "
                    />
                  </div>
                </template>
                <template #default>
                  <div class="custom-tooltip">
                    <div class="tooltip-item">
                      <div class="tooltip-header">
                        <span class="color-dot" :style="{ backgroundColor: '#FF761A' }"></span>
                        <span class="label">临近过保：</span>
                      </div>
                      <div class="tooltip-footer">
                        <span>数量: {{ props.projectCount.buildNearNoSafeguardCount }}</span>
                        <span class="percentage" :style="{ color: '#FF761A' }"
                          >{{ buildRatio2 }}%</span
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </el-popover>
            </div>

            <!-- 已过保 -->
            <div class="line">
              <p class="content">
                <span>已过保:</span>
                <span>{{ props.projectCount.buildNoSafeguardCount }}</span>
              </p>
              <el-popover
                placement="right"
                trigger="hover"
                :show-arrow="false"
                popper-style="padding: 0; border: none; background: transparent;"
              >
                <template #reference>
                  <div class="progress-wrapper">
                    <el-progress
                      :text-inside="true"
                      :stroke-width="ProgressHeight"
                      :percentage="buildRatio3"
                      color="#FF3B48"
                      :show-text="buildRatio3 > 0"
                      :class="
                        getProgressClass(buildRatio3) === 'custom-progress'
                          ? 'custom-progress'
                          : [getProgressClass(buildRatio3), 'color3']
                      "
                    />
                  </div>
                </template>
                <template #default>
                  <div class="custom-tooltip">
                    <div class="tooltip-item">
                      <div class="tooltip-header">
                        <span class="color-dot" :style="{ backgroundColor: '#FF3B48' }"></span>
                        <span class="label">已过保：</span>
                      </div>
                      <div class="tooltip-footer">
                        <span>数量: {{ props.projectCount.buildNoSafeguardCount }}</span>
                        <span class="percentage" :style="{ color: '#FF3B48' }"
                          >{{ buildRatio3 }}%</span
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </el-popover>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps } from 'vue'
import type { TabsPaneContext } from 'element-plus'

const ProgressHeight = ref(20) //进度条高度统一管理
// 计算样式函数
const getProgressClass = (ratio: number) => {
  if (ratio > 14) return 'custom-progress'
  if (ratio >= 11 && ratio <= 14) return 'custom-progress2'
  if (ratio >= 7 && ratio < 11) return 'custom-progress3'
  if (ratio >= 3 && ratio < 7) return 'custom-progress4'
  if (ratio >= 1 && ratio < 3) return 'custom-progress5'
  return ''
}

const activeName = ref('first')
// 定义 props，接收父组件传递的 ProjectCount
const props = defineProps({
  projectCount: {
    type: Object,
    required: true
  }
})
const oamRatio1 = ref(0)
const oamRatio2 = ref(0)
const oamRatio3 = ref(0)

const buildRatio1 = ref(0)
const buildRatio2 = ref(0)
const buildRatio3 = ref(0)

onMounted(() => {
  // 在子组件中使用 props.projectCount 即可获取父组件传递的数据
})
// 改为watch监听方便渲染进度条，onMounted无法做到
watch(
  () => props.projectCount,
  (newVal) => {
    if (newVal) {
      // // 运维项目的进度条
      // oamRatio1.value =
      //   newVal.oamCount > 0 ? Math.floor((newVal.oamSafeguardCount / newVal.oamCount) * 100) : 0
      // oamRatio2.value =
      //   newVal.oamCount > 0
      //     ? Math.floor((newVal.oamNearNoSafeguardCount / newVal.oamCount) * 100)
      //     : 0
      // oamRatio3.value =
      //   newVal.oamCount > 0 ? Math.floor((newVal.oamNoSafeguardCount / newVal.oamCount) * 100) : 0

      // // 建设项目的进度条
      // buildRatio1.value =
      //   newVal.buildCount > 0
      //     ? Math.floor((newVal.buildSafeguardCount / newVal.buildCount) * 100)
      //     : 0
      // buildRatio2.value =
      //   newVal.buildCount > 0
      //     ? Math.floor((newVal.buildNearNoSafeguardCount / newVal.buildCount) * 100)
      //     : 0
      // buildRatio3.value =
      //   newVal.buildCount > 0
      //     ? Math.floor((newVal.buildNoSafeguardCount / newVal.buildCount) * 100)
      //     : 0
      // ↓保留2位小数 ↑不保留两位小数向下取整
      // 运维项目的进度条
      oamRatio1.value =
        newVal.oamCount > 0
          ? Number(((newVal.oamSafeguardCount / newVal.oamCount) * 100).toFixed(2))
          : 0
      oamRatio2.value =
        newVal.oamCount > 0
          ? Number(((newVal.oamNearNoSafeguardCount / newVal.oamCount) * 100).toFixed(2))
          : 0
      oamRatio3.value =
        newVal.oamCount > 0
          ? Number(((newVal.oamNoSafeguardCount / newVal.oamCount) * 100).toFixed(2))
          : 0

      // 建设项目的进度条
      buildRatio1.value =
        newVal.buildCount > 0
          ? Number(((newVal.buildSafeguardCount / newVal.buildCount) * 100).toFixed(2))
          : 0
      buildRatio2.value =
        newVal.buildCount > 0
          ? Number(((newVal.buildNearNoSafeguardCount / newVal.buildCount) * 100).toFixed(2))
          : 0
      buildRatio3.value =
        newVal.buildCount > 0
          ? Number(((newVal.buildNoSafeguardCount / newVal.buildCount) * 100).toFixed(2))
          : 0
    }
  },
  { immediate: true, deep: true }
)
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
</script>

<style lang="scss" scoped>
.left-bottom-tabs {
  width: 100%;
  height: 100%;
  // background-color: yellowgreen;
  border-radius: 0 5px 5px 0; //边框圆角
  overflow: hidden;
  /* 使用 ::v-deep 确保样式能够渗透到子组件 */
  ::v-deep .el-tabs__nav {
    width: 100%;
  }

  /* 调整导航标签的样式 */
  ::v-deep .el-tabs__item {
    font-size: 14px !important;
    padding: 0 6px !important; //调整左右边距
  }

  /* 调整 tab 的活动和非活动状态下的样式 */
  ::v-deep .el-tabs__item.is-active {
    font-size: 14px !important;
    font-weight: bold;
    color: #000 !important; /* 选中的文字颜色为黑色 */
  }

  /* 调整底部线为黄色 */
  ::v-deep .el-tabs__active-bar {
    background-color: #ffbc1c !important;
  }
  .demo-tabs > .el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
    height: 100% !important;
    .main {
      width: 100%;
      height: 100%;

      .title-box {
        width: 100%;
        // height: 40px;
        // line-height: 40px;
        height: 50px;
        line-height: 50px;
        background-color: #f6f6f6;
        display: flex;
        justify-content: space-between;
        border-radius: 3px;
        font-size: 14px;
        span:nth-child(2) {
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
  }
}
.demo-progress .el-progress--line {
  margin-bottom: 15px;
}
.el-progress--line {
  height: 30px !important;
  .el-progress-bar__innerText {
    line-height: 30px !important;
  }
}
.line {
  .content {
    display: flex;
    justify-content: space-between;
    // font-size: 12px;
    font-size: 14px;
    padding: 10px 0;
    span:nth-child(1) {
      padding-bottom: 5px;
    }
  }
}
// 进度条内样式
.custom-progress {
  ::v-deep .el-progress-bar__innerText {
    // font-size: 8px !important;
    font-size: 10px !important;
    display: flex;
    align-items: center;
    justify-content: end;
    height: 100%;
  }
}

// 11~14%的样式
.custom-progress2 {
  position: relative;
  ::v-deep .el-progress-bar__innerText {
    position: absolute;
    top: 3px;
    left: 28px;
    font-size: 10px !important;
  }
}
// 7~11%的样式
.custom-progress3 {
  position: relative;
  ::v-deep .el-progress-bar__innerText {
    position: absolute;
    top: 3px;
    left: 20px;
    font-size: 10px !important;
  }
}
// 3~7%的样式
.custom-progress4 {
  position: relative;
  ::v-deep .el-progress-bar__innerText {
    position: absolute;
    top: 3px;
    left: 15px;
    font-size: 10px !important;
  }
}
// 1~3%的样式
.custom-progress5 {
  position: relative;
  ::v-deep .el-progress-bar__innerText {
    position: absolute;
    top: 3px;
    left: 10px;
    font-size: 10px !important;
  }
}

// 百分比文字颜色
.color1 {
  ::v-deep .el-progress-bar__innerText {
    color: #0fc983;
  }
}
.color2 {
  ::v-deep .el-progress-bar__innerText {
    color: #ff761a;
  }
}
.color3 {
  ::v-deep .el-progress-bar__innerText {
    color: #ff3b48;
  }
}
// 提示框样式  (暂缓先处理其他)
.custom-tooltip {
  // max-width: 200px;
  // padding: 10px;
  // background-color: white;
  // border-radius: 4px;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  min-width: 120px;
  // max-width: 300px;//取消最大宽度限制防止溢出宽度换行
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  white-space: nowrap; // 防止换行
  font-size: 14px;
}

.tooltip-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px; // 上下间距
}

.color-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
}

.tooltip-details {
  display: flex;
  justify-content: space-between;
  margin-left: 5px;
  font-size: 14px;
  color: #666;
}
// 弹窗样式
.custom-tooltip {
  max-width: 200px;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  font-size: 14px;
}

.tooltip-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start; // 左对齐
}

.tooltip-header {
  display: flex;
  align-items: center;
  // gap: 8px;//圆点和标题之间的距离
}

.label {
  font-weight: bold; // 加粗文字
}

.tooltip-footer {
  display: flex;
  margin-left: 18px;
  gap: 16px;
  align-items: center;
}

.tooltip-footer .percentage {
  white-space: nowrap;
}
</style>
