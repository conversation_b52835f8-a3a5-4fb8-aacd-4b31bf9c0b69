<template>
  <div class="main-bottom">
    <div class="title-box">设备总览</div>
    <div class="main-box">
      <div class="left-box">
        <div class="left-top" ref="chartDom"></div>
        <div class="left-bottom">
          <div class="bottom-box">
            <div class="img-box">
              <img src="@/assets/imgs/icons/equipmentIconBig.png" alt="" />
              <p>设备总数</p>
            </div>
            <div class="all-box">{{ props.equipmentBasis.deviceTotal.toLocaleString() }}</div>
          </div>
        </div>
      </div>
      <div class="right-box">
        <div class="right-top">
          <div class="right-top-title-box">
            <el-radio-group v-model="tabPosition" class="tabs-style">
              <el-radio-button value="maintenanceUnit">维保单位</el-radio-button>
              <el-radio-button value="deviceType">设备类型</el-radio-button>
              <el-radio-button value="project">项目</el-radio-button>
              <el-radio-button value="area">所属区域</el-radio-button>
            </el-radio-group>
          </div>
          <div class="echarts-box" ref="chartDom2"></div>
        </div>
        <div class="right-bottom">
          <div class="right-bottom-title-box">
            <el-radio-group v-model="tabPosition2" class="tabs-style">
              <el-radio-button value="deviceType">设备类型</el-radio-button>
              <el-radio-button value="area">所属区域</el-radio-button>
              <el-radio-button value="maintenanceUnit">维保单位</el-radio-button>
            </el-radio-group>
          </div>
          <div class="echarts-box" ref="chartDom3"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, defineProps, watch, markRaw } from 'vue'
import * as echarts from 'echarts'

// 定义 props，接收父组件传递的 equipmentBasis
const props = defineProps({
  equipmentBasis: {
    type: Object,
    required: true
  }
})

// 在组件顶部添加分页状态变量
const currentPage = ref(0) // 当前页码
const totalPages = ref(0) // 总页数

// 在组件中添加分页指示器方法
const renderPagination = (chartInstance, pageCount) => {
  if (!chartInstance) return

  // 清除现有的分页图形
  chartInstance.setOption({
    graphic: []
  })

  if (pageCount <= 1) return

  const graphics = []
  const blockSpacing = 6 // 色块之间的间距设为 6
  const blockWidth = 40 // 分页色块宽度
  const startX = 56 - (pageCount * 10) / 2 // 居中显示  X轴位置
  // const chartWidth = chartInstance.getWidth() // 获取图表容器宽度
  // const totalBlocksWidth = pageCount * blockWidth // 所有色块总宽度
  // const startX = (chartWidth - totalBlocksWidth) / 2 // 居中

  for (let i = 0; i < pageCount; i++) {
    graphics.push({
      type: 'rect',
      z: 100,
      left: `${startX + i * blockSpacing}%`,
      top: '93%', //垂直位置
      shape: {
        width: blockWidth, //色块宽度
        height: 5, //色块高度
        r: 2
      },
      style: {
        fill: i === currentPage.value ? '#34A7FF' : '#D1DFE4',
        cursor: 'pointer'
      },
      onclick: () => {
        currentPage.value = i
        // initializeCharts()//取消更新所有图表
        updateChartForCurrentPage() //只更新第三个图表
        startCarousel(0) // 从第0条开始轮播
      }
    })
  }

  // 添加分页指示器
  chartInstance.setOption({
    graphic: graphics
  })
}

const extractData = (dataArray) => {
  const nameArray = dataArray.map((item) => item.name)
  const deviceTotalArray = dataArray.map((item) => item.deviceTotal)
  // const rateArray = dataArray.map((item) => Math.floor(item.rate))
  const rateArray = dataArray.map((item) => Number(item.rate.toFixed(2)))
  return [nameArray, deviceTotalArray, rateArray]
}

const tabPosition = ref('maintenanceUnit') // 初始化tab默认选中
const tabPosition2 = ref('deviceType') // 初始化tab默认选中

const chartDom = ref(null)
const myChart = ref(null)
const chartDom2 = ref(null)
const myChart2 = ref(null)
const chartDom3 = ref(null)
const myChart3 = ref(null)

// 轮播变量
const popupIndex = ref(0)
const carouselIntervalId = ref(null)
let popupDataList = []
nextTick(() => {
  showTooltipAtIndex(popupIndex.value)
})

const initializeChart = (dom, chartRef, option, bindHover = true) => {
  if (chartRef.value) {
    chartRef.value.dispose()
  }

  chartRef.value = markRaw(echarts.init(dom))
  chartRef.value.setOption(option)

  // 只对 chartDom3 绑定 hover 控制
  if (bindHover) {
    dom.addEventListener('mouseenter', () => {
      stopCarousel()
    })

    dom.addEventListener('mouseleave', () => {
      startCarousel()
    })

    setTimeout(() => {
      const tooltipEl = dom.querySelector('.echarts-tooltip')
      if (tooltipEl) {
        tooltipEl.addEventListener('mouseenter', () => {
          stopCarousel()
        })
        tooltipEl.addEventListener('mouseleave', () => {
          startCarousel()
        })
      }
    }, 1500)
  }
}
// 调整图表大小
const resizeChart = (chartRef) => {
  if (chartRef.value) {
    nextTick(() => {
      try {
        chartRef.value.resize()
      } catch (error) {
        console.error('Error resizing chart:', error)
      }
    })
  }
}

// 初始化所有图表
const initializeCharts = () => {
  if (chartDom.value) {
    // console.log('DOM 存在，宽高:', chartDom.value.offsetWidth, 'x', chartDom.value.offsetHeight)

    const option1 = {
      title: [
        {
          text:
            Number(
              (props.equipmentBasis.rate == null
                ? 0
                : parseFloat(props.equipmentBasis.rate) || 0
              ).toFixed(2)
            ) + '%',
          subtext: '总完好率',
          textStyle: {
            fontSize: 20,
            color: '#00c292', // 设置为绿色
            fontWeight: 'bold'
          },
          subtextStyle: {
            fontSize: 12,
            color: '#000'
          },
          x: 'center',
          y: 'center'
        }
      ],
      tooltip: { show: false },
      legend: { show: false },
      polar: {
        radius: ['60%', '80%'], // 调整圆环的内外半径
        center: ['50%', '55%'] // 调整圆环位置
      },
      angleAxis: {
        max: 100,
        show: false,
        startAngle: -90, // 设置起始角度为-90度（6点钟方向）
        clockwise: true
      },
      radiusAxis: {
        type: 'category',
        show: true,
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false }
      },
      series: [
        // 新增：最内层小圆环（包裹文字）
        {
          type: 'pie',
          radius: ['50%', '61%'],
          center: ['50%', '55%'],
          itemStyle: {
            borderRadius: 2,
            borderColor: '#fff',
            borderWidth: 0,
            color: '#f5f7fa' // 可自定义颜色
          },
          data: [{ value: 100 }],
          silent: true,
          z: -1
        },

        // 背景圆环（浅绿色）
        {
          type: 'bar',
          stack: 'round',
          name: '背景',
          silent: true,
          roundCap: true,
          barWidth: 22,
          showBackground: false,
          coordinateSystem: 'polar',
          itemStyle: {
            color: '#e6f7ea' // 浅绿色背景
          },
          data: [100], // 背景圆环完整显示
          clockWise: true
        },

        // 前景圆环（绿色）
        {
          type: 'bar',
          stack: 'round',
          name: '当前值',
          silent: true,
          roundCap: true,
          barWidth: 28,
          showBackground: false,
          coordinateSystem: 'polar',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#11C896' }, // 绿色
              { offset: 1, color: '#19BC76' }
            ])
          },
          data: [
            props.equipmentBasis.rate > 91 && props.equipmentBasis.rate < 96
              ? parseFloat(props.equipmentBasis.rate - 3)
              : parseFloat(props.equipmentBasis.rate) || 0
          ], // 使用当前完好率
          clockWise: true
        },

        // 指示器圆点（白色）
        {
          type: 'effectScatter',
          coordinateSystem: 'polar',
          data:
            props.equipmentBasis.rate !== 100 ? [parseFloat(props.equipmentBasis.rate - 1.5)] : [], // 完好率是 100 时不显示
          symbolSize: 10, // 圆点大小
          itemStyle: {
            color: '#fff', // 白色圆点
            borderColor: '#00c292',
            borderWidth: 2
          },
          rippleEffect: {
            brushType: 'stroke'
          },
          emphasis: {
            scale: false // 禁用悬停动画
          }
        }
      ]
    }

    initializeChart(chartDom.value, myChart, option1, false)
  }

  if (chartDom2.value) {
    const [nameArr, deviceTotalArr, rateArr] = extractData(props.equipmentBasis[tabPosition.value])
    if (chartDom2.value) {
      // 确保数据至少有3条，不足时填充空数据
      const targetLength = 3
      const paddedNameArr = [
        ...nameArr,
        ...Array(Math.max(targetLength - nameArr.length, 0)).fill('')
      ]
      const paddedDeviceTotalArr = [
        ...deviceTotalArr,
        ...Array(Math.max(targetLength - deviceTotalArr.length, 0)).fill(0)
      ]
      const paddedRateArr = [
        ...rateArr,
        ...Array(Math.max(targetLength - rateArr.length, 0)).fill(0)
      ]

      const option2 = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: function (params) {
            let result = params[0].name
            params.forEach((param) => {
              const value = param.seriesName === '运维设备' ? param.value : param.value + '%'
              result += `<br/>${param.seriesName}: <span style='font-weight:bold;'>${value}</span>`
            })
            return result
          }
        },
        legend: {
          data: ['运维设备', '完好率'],
          left: 'left',
          top: '6px',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: { fontSize: 12 }
        },
        grid: {
          left: '0%',
          right: '5%', //距离右侧滚动条的距离
          bottom: '-10%', // 稳定布局
          top: '15%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'value',
            max: props.equipmentBasis.deviceTotal,
            show: false,
            offset: 10
          },
          {
            type: 'value',
            max: 100,
            show: false,
            offset: -10
          }
        ],
        yAxis: [
          {
            type: 'category',
            data: paddedNameArr,
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              color: '#606266',
              fontSize: 12,
              margin: 10,
              formatter: (name) => name || ''
            },
            inverse: true
          },
          {
            type: 'category',
            axisLabel: {
              show: true,
              formatter: function (params) {
                const index = paddedNameArr.indexOf(params)
                if (index !== -1) {
                  const deviceTotal = paddedDeviceTotalArr[index]
                  const rate = paddedRateArr[index]

                  const deviceLabel = deviceTotal > 0 ? `{a|${deviceTotal}}` : ''
                  const rateLabel = rate > 0 ? `{b|${rate}%}` : ''

                  return [deviceLabel, rateLabel].join('\n')
                }
                return ['{a|}', '{b|}'].join('\n')
              },
              // formatter: function (params) {
              //   const index = paddedNameArr.indexOf(params)
              //   if (index !== -1) {
              //     return [
              //       `{a|${paddedDeviceTotalArr[index]}}`,
              //       `{b|${paddedRateArr[index]}%}`
              //     ].join('\n')
              //   }
              //   return ['{a|}', '{b|}'].join('\n')
              // },
              rich: {
                a: {
                  color: '#FDA915',
                  fontSize: 12,
                  fontWeight: 'bold'
                },
                b: {
                  color: '#3EF09D',
                  fontSize: 12,
                  fontWeight: 'bold'
                }
              }
            },
            inverse: true,
            splitLine: { show: false },
            axisTick: { show: false },
            axisLine: { show: false },
            data: paddedNameArr
          }
        ],
        series: [
          {
            name: '运维设备',
            type: 'bar',
            xAxisIndex: 0,
            barWidth: 8,
            barGap: '80%',
            showBackground: true,
            backgroundStyle: {
              color: '#D1DFE4',
              borderRadius: 0
            },
            data: paddedDeviceTotalArr,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#FDA915' },
                { offset: 1, color: '#FFDF15' }
              ]),
              borderRadius: 0
            },
            // itemStyle: {
            //   color: function (params) {
            //     if (params.dataIndex >= deviceTotalArr.length) {
            //       return '#D1DFE4'
            //     }
            //     return new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            //       { offset: 0, color: '#FDA915' },
            //       { offset: 1, color: '#FFDF15' }
            //     ])
            //   },
            //   borderRadius: 0
            // },
            silent: true
          },
          {
            name: '完好率',
            type: 'bar',
            xAxisIndex: 1,
            barWidth: 8,
            barGap: '80%',
            showBackground: true,
            backgroundStyle: {
              color: '#D1DFE4',
              borderRadius: 0
            },
            data: paddedRateArr,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#3EF09D' }, // 开始颜色
                { offset: 1, color: '#10E187' } // 结束颜色
              ]),
              borderRadius: 0
            },
            // itemStyle: {
            //   color: function (params) {
            //     if (params.dataIndex >= rateArr.length) {
            //       return '#D1DFE4'
            //     }
            //     return new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            //       { offset: 0, color: '#3EF09D' },
            //       { offset: 1, color: '#10E187' }
            //     ])
            //   },
            //   borderRadius: 0
            // },
            silent: true
          }
        ],
        // 有阴影
        dataZoom: [
          {
            type: 'slider',
            show: nameArr.length > 3,
            start: 0,
            end: Math.min(100, (3 / nameArr.length) * 100), // 显示前3条
            backgroundColor: 'rgba(250, 250, 255, 0.9)',
            fillerColor: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(52, 167, 255, 0.6)' },
              { offset: 1, color: 'rgba(52, 167, 255, 0.2)' }
            ]),
            borderColor: 'rgba(200, 200, 200, 0.5)',
            borderRadius: 4,
            showDetail: false,
            yAxisIndex: [0, 1],
            filterMode: 'empty',
            width: 10,
            right: 8,
            top: '10%',
            bottom: '10%',
            height: '80%',
            handleSize: 16,
            handleStyle: {
              color: '#34A7FF',
              borderColor: '#fff',
              borderWidth: 2,
              borderRadius: '50%',
              shadowBlur: 4,
              shadowColor: 'rgba(0, 0, 0, 0.2)',
              shadowOffsetX: 0,
              shadowOffsetY: 2
            },
            zoomLock: true,
            zoomOnMouseWheel: false,
            moveOnMouseMove: false,
            moveOnMouseWheel: true // 启用滚轮滑动
          }
        ]
        // 无阴影
        // dataZoom: [
        //   {
        //     type: 'slider',
        //     show: nameArr.length > 3,
        //     start: 0,
        //     end: Math.min(40, (3 / nameArr.length) * 70),
        //     backgroundColor: 'rgba(250, 250, 250, 0.9)',
        //     fillerColor: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        //       { offset: 0, color: 'rgba(52, 167, 255, 0.6)' },
        //       { offset: 1, color: 'rgba(52, 167, 255, 0.2)' }
        //     ]),
        //     borderColor: 'rgba(200, 200, 200, 0.5)',
        //     borderRadius: 4,
        //     showDetail: false,
        //     yAxisIndex: [0, 1],
        //     filterMode: 'empty',
        //     width: 10,
        //     right: 8,
        //     top: '30%',
        //     height: '70%',
        //     handleSize: 16,
        //     handleStyle: {
        //       color: '#34A7FF',
        //       borderColor: '#fff',
        //       borderWidth: 2,
        //       borderRadius: '50%',
        //       shadowBlur: 4,
        //       shadowColor: 'rgba(0, 0, 0, 0.2)',
        //       shadowOffsetX: 0,
        //       shadowOffsetY: 2
        //     },
        //     zoomLock: true, // 锁定选择区域大小，禁止拉伸
        //     zoomOnMouseWheel: false, // 禁止滚轮缩放
        //     moveOnMouseMove: false, // 禁止鼠标移动调整视图
        //     moveOnMouseWheel: false // 禁止滚轮移动视图
        //   }
        // ]
      }

      initializeChart(chartDom2.value, myChart2, option2, false)
    }
  }

  if (chartDom3.value) {
    // 直接调用 updateChartForCurrentPage 来初始化图表
    // updateChartForCurrentPage()

    const [nameArr, deviceTotalArr, rateArr] = extractData(props.equipmentBasis[tabPosition2.value])

    const option3 = {
      grid: {
        left: '5%',
        right: '5%',
        bottom: '10%', // 增加底部空间用于分页指示器
        top: '15%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        enterable: true,
        textStyle: {
          fontSize: 12
        },
        extraCssText: 'z-index: 10;',
        formatter: function (params) {
          if (!params || params.length === 0 || !params[0]) {
            return ''
          }
          const category = params[0].name || ''
          const value = params[0].value || ''
          return `${category}<br>数量: <span style="color: #34A7FF">${value}</span>`
        }
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          interval: 0,
          // rotate: 30, // 旋转标签避免重叠
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        name: '数量',
        nameTextStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '数量',
          data: [],
          type: 'bar',
          barWidth: 22,
          itemStyle: {
            color: '#34A7FF'
          }
        }
      ]
    }

    // 修改初始化图表函数中的 option3 部分
    if (chartDom3.value) {
      const [nameArr, deviceTotalArr] = extractData(props.equipmentBasis[tabPosition2.value])

      // 计算分页
      const pageSize = 8 // 每页显示8条数据
      totalPages.value = Math.ceil(nameArr.length / pageSize)

      // 获取当前页数据
      const startIndex = currentPage.value * pageSize
      const endIndex = Math.min(startIndex + pageSize, nameArr.length)
      const currentPageNames = nameArr.slice(startIndex, endIndex)
      const currentPageValues = deviceTotalArr.slice(startIndex, endIndex)

      // 更新图表数据
      option3.xAxis.data = currentPageNames
      option3.series[0].data = currentPageValues

      initializeChart(chartDom3.value, myChart3, option3, true)

      // 添加分页指示器
      nextTick(() => {
        if (myChart3.value) {
          renderPagination(myChart3.value, totalPages.value)

          // 初始化后手动触发 tooltip 显示
          myChart3.value.dispatchAction({
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: popupIndex.value
          })
        }
      })
    }
  }
}

// 启动轮播
// 启动 tooltip 轮播
const startCarousel = (startIndex = 0) => {
  stopCarousel()

  // 只获取当前页的数据
  const pageData = props.equipmentBasis[tabPosition2.value] || []
  const startIdx = currentPage.value * 8
  const endIdx = Math.min(startIdx + 8, pageData.length)
  popupDataList = pageData.slice(startIdx, endIdx)

  if (!popupDataList.length) return

  let currentIndex = startIndex
  carouselIntervalId.value = setInterval(() => {
    showTooltipAtIndex(currentIndex)
    currentIndex = (currentIndex + 1) % popupDataList.length
  }, 1500)
}

watch(
  () => tabPosition2.value,
  () => {
    currentPage.value = 0 // 切换tab时重置到第一页
    initializeCharts()
  }
)

// 停止轮播
const stopCarousel = () => {
  clearInterval(carouselIntervalId.value)
}

// 根据索引更新图表数据
// const updateChartWithIndex = (index) => {
//   if (!myChart3.value || !props.equipmentBasis[tabPosition2.value]) return

//   const data = props.equipmentBasis[tabPosition2.value]
//   const nameArr = data.map((item) => item.name)
//   const deviceTotalArr = data.map((item) => item.deviceTotal)

//   myChart3.value.setOption({
//     xAxis: { data: nameArr },
//     series: [{ data: deviceTotalArr }]
//   })

//   // 更新后再次手动触发 tooltip 显示
//   nextTick(() => {
//     myChart3.value.dispatchAction({
//       type: 'showTip',
//       seriesIndex: 0,
//       dataIndex: 0
//     })
//   })

//   popupIndex.value = index
// }

const updateChartForCurrentPage = () => {
  if (!myChart3.value || !props.equipmentBasis[tabPosition2.value]) return

  const data = props.equipmentBasis[tabPosition2.value] || []
  const nameArr = data.map((item) => item.name)
  const deviceTotalArr = data.map((item) => item.deviceTotal)

  // 计算分页
  const pageSize = 8 // 每页显示8条数据
  totalPages.value = Math.ceil(nameArr.length / pageSize)

  // 获取当前页数据
  const startIndex = currentPage.value * pageSize
  const endIndex = Math.min(startIndex + pageSize, nameArr.length)
  const currentPageNames = nameArr.slice(startIndex, endIndex)
  const currentPageValues = deviceTotalArr.slice(startIndex, endIndex)

  // 更新图表数据
  myChart3.value.setOption({
    xAxis: { data: currentPageNames },
    series: [{ data: currentPageValues }]
  })

  // 渲染分页指示器
  renderPagination(myChart3.value, totalPages.value)

  // 重置轮播索引
  popupIndex.value = 0
}

// 显示指定索引的 tooltip
const showTooltipAtIndex = (index) => {
  if (!myChart3.value) return
  myChart3.value.dispatchAction({
    type: 'showTip',
    seriesIndex: 0,
    dataIndex: index
  })
}
// 清理图表实例
const disposeCharts = () => {
  if (myChart.value) {
    myChart.value.dispose()
  }
  if (myChart2.value) {
    myChart2.value.dispose()
  }
  if (myChart3.value) {
    myChart3.value.dispose()
  }
}

// 防抖函数
let resizeTimeout = null
const debounceResize = () => {
  clearTimeout(resizeTimeout)
  resizeTimeout = setTimeout(() => {
    resizeChart(myChart)
    resizeChart(myChart2)
    resizeChart(myChart3)
  }, 200)
}

// 监听 tabPosition 和 tabPosition2 的变化
watch([tabPosition, tabPosition2], () => {
  initializeCharts()
})

// 监听 props.equipmentBasis 的变化
watch(
  () => props.equipmentBasis,
  () => {
    initializeCharts()
  },
  { immediate: true, deep: true }
)
// 监听 tabPosition2 和 props.equipmentBasis 的变化，并初始化图表和轮播
watch(
  () => [tabPosition2.value, props.equipmentBasis],
  ([newTabValue, newEquipmentBasis]) => {
    // 构造当前 tab 的数据
    const currentData = newEquipmentBasis?.[newTabValue]

    if (currentData && currentData.length > 0) {
      popupDataList = currentData
      initializeCharts()
      nextTick(() => {
        // updateChartWithIndex(popupIndex.value)
        updateChartForCurrentPage()
        startCarousel()
      })
    } else {
      stopCarousel()
    }
  },
  // 开启深度监听
  { immediate: true, deep: true }
)

onMounted(async () => {
  await nextTick()
  // console.log('打印柱子数据', props.equipmentBasis[tabPosition2.value])
  // console.log('打印数据信息', props.equipmentBasis)
  popupDataList = props.equipmentBasis[tabPosition2.value] || []
  initializeCharts()
  await nextTick() // 确保图表渲染完成
  // updateChartWithIndex(popupIndex.value)
  updateChartForCurrentPage()
  startCarousel()

  window.addEventListener('resize', debounceResize)
})

onUnmounted(() => {
  stopCarousel() //离开页面清除轮播
  window.removeEventListener('resize', debounceResize)
  disposeCharts()
})
</script>

<style lang="scss" scoped>
.main-bottom {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  .title-box {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #3b3c3d;
    text-align: left;
    font-style: normal;
    text-transform: none;
    line-height: 24px;
    margin: 3px 0;
    padding-left: 28px;

    &::before {
      margin: 10px 0 0 10px;
      content: '';
      position: absolute;
      left: 0;
      top: 6px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #005fff;
      border: 2px solid #d4e4ff;
    }
  }
  .main-box {
    width: 100%;
    height: calc(100% - 40px);
    display: flex;
    // background-color: pink;
    .left-box {
      width: 30%;
      height: 100%;
      display: flex;
      flex-direction: column;
      .left-top {
        width: 100%;
        height: 50%;
      }
      .left-bottom {
        width: 100%;
        height: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        .bottom-box {
          width: 80%;
          height: 80%;
          border-radius: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;
          background-color: #f8fbff;
          .img-box {
            width: 60%;
            display: flex;
            flex-direction: column;
            text-align: center;
            font-size: 14px;
            // background-color: #f8fbff;
            padding-bottom: 3px;
          }
          .all-box {
            // margin-top: 5px;
            width: 100%;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            color: #005fff;
          }
        }
      }
    }
    .right-box {
      width: 70%;
      flex: 1;
      .right-top {
        width: 100%;
        height: 50%;
        position: relative;
        // background-color: plum;
        // overflow-y: auto;
        .right-top-title-box {
          position: absolute;
          right: 2%;
          width: 70%;
          height: 15%;

          .tabs-style {
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            z-index: 10 !important;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 100%;
            ::v-deep .el-radio-button__inner {
              font-size: 12px;
              padding: 5px;
            }
          }
        }
        .echarts-box {
          width: 100%;
          height: 85%;
          // background-color: pink;
          // overflow-y: auto;
        }
      }
      .right-bottom {
        width: 100%;
        height: 50%;
        position: relative;
        // background-color: purple;
        .right-bottom-title-box {
          position: absolute;
          right: 2%;
          width: 100%;
          height: 15%;
          // background-color: aqua;
          .tabs-style {
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            z-index: 10 !important;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 100%;
            ::v-deep .el-radio-button__inner {
              font-size: 12px;
              padding: 5px;
            }
          }
        }
        .echarts-box {
          width: 100%;
          height: 85%;
          // background-color: pink;
        }
      }
    }
  }
}
</style>
