<template>
  <div class="right-top">
    <div class="title-box">
      <div class="title">工单总览</div>
      <el-radio-group v-model="tabPosition" class="tabs-style">
        <el-radio-button value="1">全部</el-radio-button>
        <el-radio-button value="2">运营商</el-radio-button>
        <el-radio-button value="3">维保单位</el-radio-button>
      </el-radio-group>
    </div>
    <div class="tickets-all">
      <div><img class="w-16 h-16" src="@/assets/imgs/icons/ticketsIconBig.png" alt="" /></div>
      <div class="text-box">
        <p>工单总数</p>
        <p @click="goTicketsAll(1)" class="all-num">480</p>
      </div>
      <div class="text-box">
        <p>待处理</p>
        <p @click="goTicketsAll(1)">160</p>
      </div>
      <div class="text-box">
        <p>待审核</p>
        <p @click="goTicketsAll(1)">160</p>
      </div>
      <div class="text-box">
        <p>已完结</p>
        <p @click="goTicketsAll(1)">160</p>
      </div>
    </div>
    <div class="unit-list">
      <div class="unit-left">
        <div
          class="unit-text"
          v-for="(item, index) in menus"
          :key="item.title"
          @click="selectUnit(index)"
          :class="{ active: activeUnit === index }"
        >
          {{ item.title }}
        </div>
      </div>
      <div class="unit-right">
        <div class="right-text">
          <div class="num-box-3"><span>3</span></div>
          <div>近<span>3</span>天</div>
          <div>派发:<span>29</span></div>
          <div>报备:<span>29</span></div>
          <div>已完结:<span>29</span></div>
          <div>未完结:<span>29</span></div>
        </div>
        <div class="right-text">
          <div class="num-box-7"><span>7</span></div>
          <div>近<span>7</span>天</div>
          <div>派发:<span>29</span></div>
          <div>报备:<span>29</span></div>
          <div>已完结:<span>29</span></div>
          <div>未完结:<span>29</span></div>
        </div>
        <div class="right-text">
          <div class="num-box-30"><span>30</span></div>
          <div>近<span>30</span>天</div>
          <div>派发:<span>29</span></div>
          <div>报备:<span>29</span></div>
          <div>已完结:<span>29</span></div>
          <div>未完结:<span>29</span></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
// import type { TabsInstance } from 'element-plus'
// const tabPosition = ref<TabsInstance['tabPosition']>('1')
const tabPosition = ref('1')
const tabPosition2 = ref('1')
const menus = ref([
  { title: 'A运维单位' },
  { title: 'B运维单位' },
  { title: 'C运维单位' },
  { title: 'D运维单位' },
  { title: 'E运维单位' },
  { title: 'F运维单位' },
  { title: 'G运维单位' },
  { title: 'H运维单位' },
  { title: 'I运维单位' },
  { title: 'J运维单位' },
  { title: 'K运维单位' }
])

// 当前激活的单位索引
const activeUnit = ref(0)

// 点击单位时更新激活状态
const selectUnit = (index) => {
  activeUnit.value = index
}

// 跳转工单
const goTicketsAll = (flag) => {
  ElMessage.warning('暂未开发')
  // if (flag === 1) {
  //   router.push({
  //     path: '/alarm/list'
  //   })
  // } else if (flag === 2) {
  //   router.push({
  //     path: '/operations/alarmList'
  //     // query: { id: id.toString() }
  //   })
  // } else if (flag === 3) {
  // }
}

// 页面加载时默认激活第一个单位
onMounted(() => {
  selectUnit(0)
})
</script>

<style lang="scss" scoped>
.right-top {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .title-box {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center; /* 确保子元素垂直居中 */
    margin-bottom: 5px; //增加下边距左右盒子位置平行
    .title {
      padding-left: 20px;
      line-height: 40px; /* 确保文本垂直居中 */
      height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 16px;
      color: #3b3c3d;
      text-align: left;
      font-style: normal;
      text-transform: none;
      line-height: 24px;
      margin: 3px 0;
      padding-left: 28px;

      &::before {
        margin: 10px 0 0 10px;
        content: '';
        position: absolute;
        left: 0;
        top: 6px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #005fff;
        border: 2px solid #d4e4ff;
      }
    }
    .tabs-style {
      padding-right: 10px; //设置边距
      display: flex;
      justify-content: flex-end;
      align-items: center; /* 确保子元素垂直居中 */
      height: 100%;
      ::v-deep .el-radio-button__inner {
        font-size: 12px;
        padding: 5px;
      }
    }
  }
  .tickets-all {
    // width: 100%;
    width: 96%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #f5f9ff;
    margin-bottom: 15px;
    .text-box {
      .all-num {
        color: #005fff;
        text-align: center;
      }
      p:nth-child(1) {
        font-size: 14px;
      }
      p:nth-child(2) {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
      }
    }
  }
  .unit-list {
    // width: 100%;
    width: 96%;
    // height: 157px;
    height: 207px;
    // flex: 1;
    display: flex;
    .unit-left {
      width: 30%;
      height: 100%;
      overflow-y: auto;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      padding-bottom: 30px;
      .unit-text {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        text-align: center;
        cursor: pointer;
        &:hover {
          background-color: #f2f2f2;
        }
        &.active {
          background-color: #f5f9ff;
          color: #005fff;
          border-right: 2px solid #005fff;
        }
      }
    }
    .unit-right {
      width: 70%;
      height: 100%;
      background-color: #f7f8f8;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      padding: 5px 5px 0 5px;
      .right-text {
        width: 100%;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        background-color: white;
        border-radius: 3px;
        font-size: 14px;
        span {
          font-weight: bold;
        }
        .num-box-3 {
          width: 25px;
          height: 25px;
          line-height: 25px;
          border-radius: 12px;
          background-color: #005fff;
          text-align: center;
          color: white;
        }
        .num-box-7 {
          width: 25px;
          height: 25px;
          line-height: 25px;
          border-radius: 12px;
          background-color: #4c8fff;
          text-align: center;
          color: white;
        }
        .num-box-30 {
          width: 25px;
          height: 25px;
          line-height: 25px;
          border-radius: 12px;
          background-color: #7fafff;
          text-align: center;
          color: white;
        }
      }
    }
  }
}
</style>
