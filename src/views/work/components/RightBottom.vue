<template>
  <div class="right-bottom">
    <div class="title-box"> 维护总览 </div>
    <div class="maintenance-all p-r-2">
      <div class="shadow-box">
        <div class="echarts-2" ref="chartDom2"></div>
        <div class="category-box">
          <div class="progress-container">
            <el-progress
              type="circle"
              :stroke-width="4"
              :percentage="30"
              :width="40"
              color="#FF875A"
            />
          </div>
          <div class="text">
            <div>未开始</div>
            <div>299</div>
          </div>
        </div>
        <div class="category-box">
          <div class="progress-container2">
            <el-progress
              type="circle"
              :stroke-width="4"
              :percentage="30"
              :width="40"
              color="#37A9FF"
            />
          </div>

          <div class="text">
            <div>进行中</div>
            <div>299</div>
          </div>
        </div>
        <div class="category-box">
          <div class="progress-container3">
            <el-progress
              type="circle"
              :stroke-width="4"
              :percentage="40"
              :width="40"
              color="#38DE8B"
            />
          </div>

          <div class="text">
            <div>已完成</div>
            <div>299</div>
          </div>
        </div>
      </div>
      <!-- ↑阴影盒子结束 -->
    </div>
    <div class="echarts-box">
      <div class="icons-box">
        <img
          v-if="activeTab === 'echarts'"
          src="@/assets/imgs/icons/overviewIcon1Blue.png"
          alt=""
          @click="navClick('echarts')"
        />
        <img
          v-if="activeTab !== 'echarts'"
          src="@/assets/imgs/icons/overviewIcon1.png"
          alt=""
          @click="navClick('echarts')"
        />
        <img
          v-if="activeTab === 'table'"
          src="@/assets/imgs/icons/overviewIcon2Blue.png"
          alt=""
          @click="navClick('table')"
        />
        <img
          v-if="activeTab !== 'table'"
          src="@/assets/imgs/icons/overviewIcon2.png"
          alt=""
          @click="navClick('table')"
        />
      </div>

      <div v-if="activeTab === 'echarts'" class="echarts-style" ref="chartDom"></div>
      <div v-else-if="activeTab === 'table'" class="table-box">
        <el-table :data="infoList" style="width: 100%; padding: 0 15px 0 15px" height="100%">
          <el-table-column prop="operators" label="运营商" show-overflow-tooltip />
          <el-table-column prop="ongoing" label="进行中" show-overflow-tooltip>
            <template #default="scope">
              <div class="status-dot-container">
                <span class="status-dot" style="background-color: #ff875a"></span>
                {{ scope.row.ongoing }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="noFinish" label="未完成" show-overflow-tooltip>
            <template #default="scope">
              <div class="status-dot-container">
                <span class="status-dot" style="background-color: #37a9ff"></span>
                {{ scope.row.noFinish }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="endFinish" label="已完成" show-overflow-tooltip>
            <template #default="scope">
              <div class="status-dot-container">
                <span class="status-dot" style="background-color: #38de8b"></span>
                {{ scope.row.endFinish }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="all" label="全部" show-overflow-tooltip />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, markRaw } from 'vue'
import * as echarts from 'echarts'

const activeTab = ref('echarts')
const iconFlag = ref(true)
const navClick = (navString) => {
  activeTab.value = navString
}

const chartDom = ref(null)
const myChart = ref(null)
const chartDom2 = ref(null)
const myChart2 = ref(null)

// 轮播变量
const popupIndex = ref(0)
const carouselIntervalId = ref(null)
let popupDataList = []
// 轮播函数
/**
 * 启动 tooltip 轮播
 * @param {number} startIndex - 开始索引
 */
const startCarousel = (startIndex = 0) => {
  stopCarousel()

  // 获取当前图表数据（假设 xAxis.data 是你的分类名称）
  popupDataList = myChart.value.getOption().xAxis[0].data || []

  if (!popupDataList.length) return

  let currentIndex = startIndex
  carouselIntervalId.value = setInterval(() => {
    showTooltipAtIndex(currentIndex)
    currentIndex = (currentIndex + 1) % popupDataList.length
  }, 1500)
}

/**
 * 停止 tooltip 轮播
 */
const stopCarousel = () => {
  clearInterval(carouselIntervalId.value)
}

/**
 * 显示指定索引的 tooltip
 * @param {number} index - 索引值
 */
const showTooltipAtIndex = (index) => {
  if (!myChart.value) return
  myChart.value.dispatchAction({
    type: 'showTip',
    seriesIndex: 0,
    dataIndex: index
  })
}

// 初始化图表函数
const initChart = () => {
  if (chartDom.value) {
    // 确保在初始化新实例前清理旧实例
    if (myChart.value) {
      myChart.value.dispose()
    }
    myChart.value = markRaw(echarts.init(chartDom.value))
    const option = {
      tooltip: {
        trigger: 'axis',
        enterable: true, // 允许鼠标进入 tooltip 内容区域
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params) {
          if (!params || params.length === 0) {
            return ''
          }
          const category = params[0].name
          let tooltipContent = `<div style="font-weight: bold;">${category}</div>`
          let total = 0

          // 计算总数
          params.forEach((param) => {
            total += param.value
          })

          tooltipContent += `<div style="margin: 5px 0;">维护任务总数: ${total}</div>`

          // 为每个系列添加色块和数值
          params.forEach((item) => {
            const color = item.color
            const seriesName = item.seriesName
            const value = item.value

            tooltipContent += `
          <div style="display: flex; align-items: center; margin: 3px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; margin-right: 5px; background-color: ${color};"></span>
            <span style="color: ${color};">${seriesName}: ${value}</span>
          </div>
        `
          })

          return tooltipContent
        }
      },
      legend: {
        show: true,
        orient: 'horizontal',
        left: 'center',
        top: 'top',
        icon: 'rect',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: '#4E5969'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        // top: '25%',
        top: '15%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#4E5969'
        },
        axisLine: {
          lineStyle: {
            color: '#EAEBF0'
          }
        },
        data: ['A单位', 'B单位', 'C单位', 'D单位', '其他单位']
      },
      yAxis: {
        nameTextStyle: {
          color: '#86909C',
          fontSize: 12
        },
        type: 'value',
        axisLabel: {
          color: '#4E5969'
        },
        position: 'left',
        offset: 0
      },
      series: [
        {
          name: '未开始',
          data: [300, 100, 300, 200, 200],
          type: 'bar',
          stack: 'one',
          color: '#FF875A',
          itemStyle: {
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 1)',
            borderRadius: 0
          },
          barWidth: '22' //柱子宽度
        },
        {
          name: '进行中',
          data: [500, 300, 500, 600, 500],
          type: 'bar',
          stack: 'one',
          color: '#37A9FF',
          itemStyle: {
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 1)',
            borderRadius: 0
          }
        },
        {
          name: '已完成',
          data: [400, 20, 300, 200, 200],
          type: 'bar',
          stack: 'one',
          color: '#38DE8B',
          itemStyle: {
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 1)',
            borderRadius: 0
          }
        }
      ]
    }

    myChart.value.setOption(option)
    // 初始化后手动触发 tooltip 显示
    nextTick(() => {
      showTooltipAtIndex(popupIndex.value)
    })

    startCarousel()

    // 🔥 添加鼠标进入/离开事件监听
    const chartElement = chartDom.value

    chartElement.addEventListener('mouseenter', () => {
      stopCarousel()
    })

    chartElement.addEventListener('mouseleave', () => {
      startCarousel(popupIndex.value)
    })
  }
}

// 初始化第二个图表函数
const initChart2 = () => {
  if (chartDom2.value) {
    if (myChart2.value) {
      myChart2.value.dispose()
    }
    const dataList = [
      { name: '类别1', value: 15, color: '#FF875A' },
      { name: '类别2', value: 25, color: '#38DE8B' },
      { name: '类别3', value: 60, color: '#37A9FF' }
    ]
    myChart2.value = echarts.init(chartDom2.value)
    const option2 = {
      legend: {
        show: false,
        orient: 'horizontal',
        itemWidth: 10,
        itemHeight: 10,
        bottom: 0,
        left: 'center',
        textStyle: {
          color: '#808080'
        },
        data: dataList.map((item) => item.name)
      },
      series: [
        {
          name: '项目信息',
          type: 'pie',
          // radius: ['70%', '90%'],
          radius: ['80%', '100%'],
          center: ['50%', '50%'],
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          itemStyle: {
            color: function (params) {
              return dataList[params.dataIndex].color
            },
            shadowBlur: 10,
            shadowColor: '#fff',
            borderWidth: 2,
            borderColor: '#fff'
          },
          data: dataList
        },
        {
          type: 'pie',
          radius: ['71%', '75%'],
          center: ['50%', '50%'],
          labelLine: {
            show: false
          },
          itemStyle: {
            color: '#f5f7fa', // 背景色
            shadowBlur: 3, // 阴影模糊程度
            shadowColor: 'rgba(0, 0, 0, 0.1)' // 调整阴影颜色，使阴影更明显
          },
          data: [{ value: 1 }],
          // hoverAnimation: false,//废弃
          emphasis: {
            scale: false // 新方式禁用悬停放大动画
          },
          silent: true,
          z: 999
        }
      ]
    }

    myChart2.value.setOption(option2)
    updateChart2Graphic() // 初始化时设置文字
  }
}

const updateChart2Graphic = () => {
  if (myChart2.value) {
    const optionUpdate = {
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: '35%',
          style: {
            text: '7868',
            textAlign: 'center',
            fill: '#333',
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        {
          type: 'text',
          left: 'center',
          top: '55%',
          style: {
            text: '维护总数',
            textAlign: 'center',
            fill: '#333',
            fontSize: 12
          }
        }
      ]
    }
    myChart2.value.setOption(optionUpdate)
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
    initChart2()
    window.addEventListener('resize', resizeChart)
  })
})

onUnmounted(() => {
  stopCarousel() // 清理轮播定时器
  window.removeEventListener('resize', resizeChart)
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
  if (myChart2.value) {
    myChart2.value.dispose()
    myChart2.value = null
  }
})

// const resizeChart = () => {
//   if (myChart.value) {
//     myChart.value.resize()
//   }
//   if (myChart2.value) {
//     myChart2.value.resize()
//   }
// }
const resizeChart = () => {
  if (myChart.value) {
    myChart.value.resize()
  }
  if (myChart2.value) {
    myChart2.value.resize()
    updateChart2Graphic() // 确保缩放时刷新文字内容
  }
}
// 监听 activeTab 变化，切换内容显示
watch(activeTab, (newTab) => {
  if (newTab === 'echarts') {
    nextTick(() => {
      initChart()
    })
  }
})
// 表格相关操作
const activeName = ref('first')
const handleClick = (tab, event) => {
  // console.log(tab, event)
}
const infoList = ref([
  { operators: 'A单位', ongoing: 500, noFinish: 300, endFinish: 400, all: 1200 },
  { operators: 'B单位', ongoing: 300, noFinish: 100, endFinish: 20, all: 420 },
  { operators: 'C单位', ongoing: 500, noFinish: 300, endFinish: 300, all: 1100 },
  { operators: 'D单位', ongoing: 600, noFinish: 200, endFinish: 200, all: 1000 },
  { operators: '其他单位', ongoing: 500, noFinish: 200, endFinish: 200, all: 900 }
  // { operators: 'A单位', ongoing: 500, noFinish: 300, endFinish: 400, all: 1200 },
  // { operators: 'B单位', ongoing: 300, noFinish: 100, endFinish: 20, all: 420 },
  // { operators: 'C单位', ongoing: 500, noFinish: 300, endFinish: 300, all: 1100 },
  // { operators: 'D单位', ongoing: 600, noFinish: 200, endFinish: 200, all: 1000 },
  // { operators: '其他单位', ongoing: 500, noFinish: 200, endFinish: 200, all: 900 }
])
</script>

<style lang="scss" scoped>
.right-bottom {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px !important;
  .title-box {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #3b3c3d;
    text-align: left;
    font-style: normal;
    text-transform: none;
    line-height: 24px;
    margin: 3px 0;
    padding-left: 28px;

    &::before {
      margin: 10px 0 0 10px;
      content: '';
      position: absolute;
      left: 0;
      top: 6px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #005fff;
      border: 2px solid #d4e4ff;
    }
  }
  .maintenance-all {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    .shadow-box {
      width: 100%;
      height: 70px;
      background-color: #f6f7f9;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 30px;
      .echarts-2 {
        width: 25%;
        height: 100px;
        border-radius: 0 50px 50px 0;
        // background-color: #176d5abe;
        background-color: #fff;
      }
      .category-box {
        width: 23%;
        height: 50px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); //去掉盒子阴影
        border-radius: 30px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        /* 使用 ::v-deep 确保样式应用到 el-progress 的内部结构 */
        ::v-deep .el-progress-circle {
          position: relative;
        }

        ::v-deep .el-progress__text {
          position: absolute;
          left: -3px; /* 调整这个值来控制进度条内百分比文字的水平位置 */
          top: 50%;
          transform: translateY(-50%);
          transform-origin: left center;
        }

        ::v-deep .el-progress__text-inner {
          text-align: left;
        }
        *,
        :after,
        :before {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
          font-size: 10px;
          font-weight: bold;
          color: #fff;
        }
        .text {
          display: flex;
          flex-direction: column;

          div:nth-child(1) {
            font-size: 14px;
            margin-bottom: 6px;
            font-weight: normal;
            color: #000;
          }
          div:nth-child(2) {
            font-size: 14px;
            font-weight: bold;
            color: #000;
          }
        }
        // 圆环内背景颜色
        .progress-container {
          position: relative;
          display: inline-block;
          ::v-deep .el-progress-circle {
            // 让 svg 圆形进度显示在最上层
            position: relative;
            z-index: 1;
          }

          // 使用伪元素绘制背景圆形
          &::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 83%;
            height: 83%;
            // background-color: #ffe9e2; // 中间背景色
            background-color: #ff865ac5;
            border-radius: 50%;
            z-index: 0;
          }
        }
        // 圆环内背景颜色
        .progress-container2 {
          position: relative;
          display: inline-block;
          ::v-deep .el-progress-circle {
            // 让 svg 圆形进度显示在最上层
            position: relative;
            z-index: 1;
          }

          // 使用伪元素绘制背景圆形
          &::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 83%;
            height: 83%;
            // background-color: #e4f3ff; // 中间背景色
            background-color: #37a8ffc4;
            border-radius: 50%;
            z-index: 0;
          }
        }
        // 圆环内背景颜色
        .progress-container3 {
          position: relative;
          display: inline-block;
          ::v-deep .el-progress-circle {
            // 让 svg 圆形进度显示在最上层
            position: relative;
            z-index: 1;
          }

          // 使用伪元素绘制背景圆形
          &::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 83%;
            height: 83%;
            background-color: #38de8bb3; // 中间背景色
            border-radius: 50%;
            z-index: 0;
          }
        }
      }
    }
  }
  .echarts-box {
    // flex: 1;
    width: 100%;
    height: calc(100% - 140px);
    flex-direction: column;
    // background-color: burlywood;
    position: relative;
    .icons-box {
      position: absolute;
      right: 10px;
      top: 2px;
      // background-color: antiquewhite;
      width: 15%;
      height: 10%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      z-index: 10 !important;
      img {
        cursor: pointer !important;
        z-index: 10 !important;
      }
      img:nth-child(1) {
        padding-right: 10px;
      }
    }
    .echarts-style {
      width: 100%;
      height: 90%;
      // background-color: aqua;
    }
    .table-box {
      width: 100%;
      height: 88%;
      // background-color: #999;
      position: absolute; //定位至底部
      bottom: 0;
      overflow-y: auto;
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      // 表头样式
      :deep(.el-table__header th) {
        // background-color: #409eff !important;
        font-weight: bold;
      }
    }
  }
}

// 圆点样式
.status-dot-container {
  display: flex;
  align-items: center;
  gap: 6px; // 圆点和文字之间的间距
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999; // 默认颜色，根据状态动态变化
}
</style>
