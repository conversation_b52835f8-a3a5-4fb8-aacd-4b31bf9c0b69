import type { FormRules } from 'element-plus'

/**
 * 字段类型枚举
 */
export enum FieldType {
  TEXT = 1,
  NUMBER = 2,
  DATE = 3,
  DATE_RANGE = 4,
  RADIO = 5,
  CHECKBOX = 6,
  ATTACHMENT = 7,
  TAG = 8
}

/**
 * 字段扩展配置
 */
export interface FieldConfExt {
  id?: string
  name: string
  value: string
  type: number
}

/**
 * 字段配置
 */
export interface FieldConfig {
  id: string
  code: string
  name: string
  fieldType: FieldType
  required: boolean
  addFlag?: boolean
  editFlag?: boolean
  pcViewFlag?: boolean
  fieldConfExtDOList: FieldConfExt[]
  fieldConfExtObj?: Record<string, any>
  linkage?: {
    enabled: boolean
    targetFieldId: string
    targetFieldValue: string
    condition: 'equals' | 'notEquals' | 'contains' | 'notContains' | 'startsWith' | 'endsWith'
  }
  value?: any
}

/**
 * 字段组
 */
export interface FieldGroup {
  id: string
  name?: string
  fields: FieldConfig[]
}

/**
 * 业务数据
 */
export interface BusinessData {
  id?: string
  [key: string]: any
}

/**
 * 字段选项
 */
export interface FieldOption {
  label: string
  value: string | number
  disabled?: boolean
}

/**
 * 标签信息
 */
export interface TagInfo {
  id: string
  name: string
}

/**
 * 标签表单数据
 */
export interface TagFormData {
  nodeId: string
  tagName: string
  formData: Record<string, any>
}

/**
 * 表单渲染对象
 */
export interface FormRenderItem {
  formData: Record<string, any>
  nodeId: string
  formRule: FormRules
  formRenderData: FieldGroup[]
}

/**
 * 基础配置
 */
export interface BaseConfig {
  manageId: string
  type: string
  id: string | null
  formType: number
  flagKey: string
}

/**
 * 表单配置数据
 */
export interface FormConfigData {
  formJson: string
  [key: string]: any
}

/**
 * 分页参数
 */
export interface PaginationParams {
  pageNo: number
  pageSize: number
  [key: string]: any
}

/**
 * 分页响应
 */
export interface PaginationResponse<T = any> {
  list: T[]
  total: number
  pageNo: number
  pageSize: number
}

/**
 * API 响应
 */
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

/**
 * 搜索表单数据
 */
export interface SearchFormData {
  keyword?: string
  dateRange?: [string, string]
  status?: string | number
  [key: string]: any
}

/**
 * 统计卡片数据
 */
export interface StatisticCardData {
  title: string
  value: number | string
  icon?: string
  color?: string
  trend?: {
    value: number
    isUp: boolean
  }
}

/**
 * 表格列配置
 */
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  [key: string]: any
}

/**
 * 表格数据项
 */
export interface TableDataItem {
  id: string
  [key: string]: any
}

/**
 * 操作按钮配置
 */
export interface ActionButton {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  disabled?: boolean
  loading?: boolean
  onClick: (row: TableDataItem) => void
}

/**
 * 表单验证规则配置
 */
export interface ValidationConfig {
  required?: boolean
  regex?: string
  prompt?: string
  dataValidation?: string
}

/**
 * 展示字段配置
 */
export interface ExhibitionFieldConfig {
  key: string
  label: string
  fields: string[]
}

/**
 * 树节点数据
 */
export interface TreeNodeData {
  id: string
  label: string
  value: string
  children?: TreeNodeData[]
  [key: string]: any
}

/**
 * 敏感密码表单
 */
export interface SensitivePasswordForm {
  password: string
}

/**
 * 组件 Props 类型
 */
export interface ComponentProps {
  [key: string]: any
}

/**
 * 组件 Emits 类型
 */
export interface ComponentEmits {
  [key: string]: (...args: any[]) => void
}
