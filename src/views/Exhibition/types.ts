/**
 * Exhibition 模块类型定义
 * 复用项目现有类型，避免重复定义
 */

import type { FormRules } from 'element-plus'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'

// 复用现有的类型定义
export type { FieldType } from '@/config/constants/enums/field'
export type { LabelFieldConfig as FieldConfig } from '@/config/constants/enums/fieldDefault'
export type { FieldConfExt } from '@/config/constants/enums/fieldDefault'
export type { QueryResItem as SearchFieldConfig } from '@/api/system/data/query-conf'
export type { BusinessData, SearchCondition, BusinessDataListRequest } from '@/api/system/business-data'
export type { DictDataVO as DictOption } from '@/api/system/dict/dict.data'
export type { ViewFormType } from '@/config/constants/enums/label'

// 扩展现有类型，添加字段可见性配置
export interface FieldConfigWithVisibility extends LabelFieldConfig {
  linkage?: {
    enabled: boolean
    targetFieldId: string
    targetFieldValue: string
    condition: 'equals' | 'notEquals' | 'contains' | 'notContains' | 'startsWith' | 'endsWith'
  }
  value?: any
}

/**
 * 字段组
 */
export interface FieldGroup {
  id: string
  name?: string
  fields: LabelFieldConfig[]
}

/**
 * 字段选项（兼容现有结构）
 */
export interface FieldOption {
  label: string
  value: string | number
  disabled?: boolean
}

/**
 * 标签信息
 */
export interface TagInfo {
  id: string
  name: string
}

/**
 * 标签表单数据
 */
export interface TagFormData {
  nodeId: string
  tagName: string
  formData: Record<string, any>
}

/**
 * 表单渲染对象
 */
export interface FormRenderItem {
  formData: Record<string, any>
  nodeId: string
  formRule: FormRules
  formRenderData: FieldGroup[]
}

/**
 * 基础配置
 */
export interface BaseConfig {
  manageId: string
  type: string
  id: string | null
  formType: number
  flagKey: string
}

/**
 * 表单配置数据
 */
export interface FormConfigData {
  formJson: string
  [key: string]: any
}

/**
 * 搜索表单数据
 */
export interface SearchFormData {
  keyword?: string
  dateRange?: [string, string]
  status?: string | number
  [key: string]: any
}

/**
 * 表格数据项
 */
export interface TableDataItem {
  id: string
  [key: string]: any
}

/**
 * 操作按钮配置
 */
export interface ActionButton {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  disabled?: boolean
  loading?: boolean
  onClick: (row: TableDataItem) => void
}

/**
 * 树节点数据
 */
export interface TreeNodeData {
  id: string
  label: string
  value: string
  children?: TreeNodeData[]
  [key: string]: any
}

/**
 * 敏感密码表单
 */
export interface SensitivePasswordForm {
  password: string
}
