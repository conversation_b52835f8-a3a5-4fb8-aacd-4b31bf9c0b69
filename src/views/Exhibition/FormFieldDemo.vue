<template>
  <!-- todo 删除 -->
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">FormField 组件演示</h1>

    <el-card class="mb-6">
      <template #header>
        <span>基础表单字段演示</span>
      </template>

      <el-form :model="formData" label-width="120px">
        <SimpleFormField
          v-for="field in demoFields"
          :key="field.code"
          :field="field"
          v-model="formData[field.code]"
          :field-options="getFieldOptions(field)"
        />
      </el-form>

      <div class="mt-4">
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </el-card>

    <el-card>
      <template #header>
        <span>表单数据</span>
      </template>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SimpleFormField, { FieldType } from '@/components/FormField/SimpleFormField.vue'

interface FieldConfig {
  code: string
  name: string
  fieldType: number
  remark?: string
  length?: number
  fieldConfExtDOList?: Array<{ name: string; value: any; optionsJson?: any[] }>
}

// 演示字段配置
const demoFields: FieldConfig[] = [
  { code: 'text', name: '文本输入', fieldType: FieldType.TEXT, remark: '请输入文本' },
  { code: 'textarea', name: '多行文本', fieldType: FieldType.TEXT, fieldConfExtDOList: [{ name: 'textType', value: '1', optionsJson: [] }] },
  { code: 'number', name: '数字输入', fieldType: FieldType.NUMBER },
  { code: 'radio', name: '单选框', fieldType: FieldType.RADIO, fieldConfExtDOList: [{ name: 'optionsJson', value: '', optionsJson: [
    { label: '选项A', value: 'A' },
    { label: '选项B', value: 'B' },
    { label: '选项C', value: 'C' }
  ]}] },
  { code: 'checkbox', name: '多选框', fieldType: FieldType.CHECKBOX, fieldConfExtDOList: [{ name: 'optionsJson', value: '', optionsJson: [
    { label: '选项A', value: 'A' },
    { label: '选项B', value: 'B' },
    { label: '选项C', value: 'C' }
  ]}] },
  { code: 'date', name: '日期选择', fieldType: FieldType.DATE, fieldConfExtDOList: [{ name: 'datePrecision', value: 'date', optionsJson: [] }] },
  { code: 'dateRange', name: '日期范围', fieldType: FieldType.DATE_RANGE, fieldConfExtDOList: [{ name: 'datePrecision', value: 'date', optionsJson: [] }] },
  { code: 'file', name: '文件上传', fieldType: FieldType.ATTACHMENT, fieldConfExtDOList: [
    { name: 'allowedTypes', value: 'jpg,png,pdf', optionsJson: [] },
    { name: 'countLimit', value: 3, optionsJson: [] },
    { name: 'sizeLimit', value: 10, optionsJson: [] }
  ] },
  { code: 'tag', name: '标签输入', fieldType: FieldType.TAG },
  { code: 'region', name: '地区选择', fieldType: FieldType.REGION, fieldConfExtDOList: [{ name: 'optionsJson', value: '', optionsJson: [
    { label: '北京市', value: '110000', children: [
      { label: '北京市', value: '110100', children: [
        { label: '东城区', value: '110101' },
        { label: '西城区', value: '110102' },
        { label: '朝阳区', value: '110105' }
      ]}
    ]},
    { label: '上海市', value: '310000', children: [
      { label: '上海市', value: '310100', children: [
        { label: '黄浦区', value: '310101' },
        { label: '徐汇区', value: '310104' }
      ]}
    ]}
  ]}] }
]

// 表单数据
const formData = ref<Record<string, any>>({})

// 初始化表单数据
const initFormData = () => {
  demoFields.forEach(field => {
    if (field.fieldType === FieldType.CHECKBOX) {
      formData.value[field.code] = []
    } else if (field.fieldType === FieldType.DATE_RANGE) {
      formData.value[field.code] = []
    } else {
      formData.value[field.code] = ''
    }
  })
}

// 获取字段选项
const getFieldOptions = (field: FieldConfig) => {
  // 选项现在直接从 fieldConfExtDOList 中获取，这里返回空数组作为备用
  return []
}

// 提交表单
const handleSubmit = () => {
  console.log('提交数据:', formData.value)
  ElMessage.success('表单提交成功！')
}

// 重置表单
const handleReset = () => {
  initFormData()
  ElMessage.info('表单已重置')
}

// 初始化
initFormData()
</script>