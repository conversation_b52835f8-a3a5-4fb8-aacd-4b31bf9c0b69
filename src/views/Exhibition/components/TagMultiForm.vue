<template>
  <Dialog
    v-model="visible"
    title="多标签动态表单"
    width="70%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="flex gap-8 max-h-[50vh]">
      <!-- 左侧树形区块 -->
      <div class="w-72 min-w-60 bg-gray-50 border border-gray-200 rounded-lg p-4 flex-shrink-0 overflow-auto max-h-[50vh]">
        <el-tree
          :data="treeData"
          node-key="value"
          show-checkbox
          default-expand-all
          :props="treeProps"
          :check-strictly="true"
          @check="onTreeCheck"
          ref="treeRef"
        />
      </div>

      <!-- 右侧动态表单 -->
      <div class="w-[900px] min-w-0 overflow-auto max-h-[50vh]">
        <el-space direction="vertical" :size="24" fill>
          <el-card
            v-for="tag in selectedTags"
            :key="tag"
            shadow="hover"
            class="border border-gray-200 rounded-xl bg-white w-full"
            body-class="p-6 w-[600px]"
          >
            <div class="font-bold text-lg mb-4 text-gray-700">{{ getTagLabel(tag) }}</div>
            <el-form :model="formData[tag]" label-width="80px">
              <SimpleFormField
                v-for="field in getFieldsByTag(tag)"
                :key="field.code"
                :field="field"
                v-model="formData[tag][field.code]"
                :field-options="getFieldOptions(field)"
              />
            </el-form>
          </el-card>
        </el-space>
      </div>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch, type Ref } from 'vue'
import SimpleFormField, { FieldType } from '@/components/FormField/SimpleFormField.vue'

// 简化的字段配置接口，适配现有组件
interface FieldConfig {
  code: string
  name: string
  fieldType: number
  remark?: string
  fieldConfExtDOList?: Array<{ name: string; value: any; optionsJson?: any[] }>
}

const emit = defineEmits(['ok'])

const visible = ref(false)
const treeRef = ref()

// 树形标签数据
const treeData = [
  {
    label: '重点人员', value: 'important', children: [
      {
        label: '关怀对象', value: 'care', children: [
          {
            label: '关爱人员', value: 'love', children: [
              { label: '困境儿童', value: 'child' },
              { label: '信访人员', value: 'visit' }
            ]
          }
        ]
      }
    ]
  }
]
const treeProps = { label: 'label', children: 'children' }

// 选中的标签（叶子节点 value）
const selectedTags = ref<string[]>([])

// 每个标签对应的字段配置（可从接口获取）
const tagFieldMap: Ref<Record<string, FieldConfig[]>> = ref({
  child: [
    {
      code: 'name',
      name: '姓名',
      fieldType: FieldType.TEXT,
      remark: '输入姓名',
      fieldConfExtDOList: [
        { name: 'textType', value: '0', optionsJson: [] }
      ]
    },
    {
      code: 'idCard',
      name: '身份证',
      fieldType: FieldType.TEXT,
      remark: '身份证号',
      fieldConfExtDOList: [
        { name: 'textType', value: '0', optionsJson: [] }
      ]
    },
    {
      code: 'age',
      name: '年龄',
      fieldType: FieldType.NUMBER,
      remark: '',
      fieldConfExtDOList: [
        { name: 'numberType', value: '0', optionsJson: [] }
      ]
    },
    {
      code: 'status',
      name: '状态',
      fieldType: FieldType.RADIO,
      remark: '',
      fieldConfExtDOList: [
        { name: 'optionsJson', value: '', optionsJson: [
          { label: '正常', value: '1' },
          { label: '异常', value: '2' }
        ]}
      ]
    }
  ],
  visit: [
    {
      code: 'name',
      name: '姓名',
      fieldType: FieldType.TEXT,
      remark: '输入姓名',
      fieldConfExtDOList: [
        { name: 'textType', value: '0', optionsJson: [] }
      ]
    },
    {
      code: 'idCard',
      name: '身份证',
      fieldType: FieldType.TEXT,
      remark: '身份证号',
      fieldConfExtDOList: [
        { name: 'textType', value: '0', optionsJson: [] }
      ]
    },
    {
      code: 'visitType',
      name: '信访类型',
      fieldType: FieldType.CHECKBOX,
      remark: '',
      fieldConfExtDOList: [
        { name: 'optionsJson', value: '', optionsJson: [
          { label: '经济纠纷', value: '1' },
          { label: '土地纠纷', value: '2' },
          { label: '其他', value: '3' }
        ]}
      ]
    },
    {
      code: 'visitDate',
      name: '信访日期',
      fieldType: FieldType.DATE,
      remark: '',
      fieldConfExtDOList: [
        { name: 'datePrecision', value: 'date', optionsJson: [] }
      ]
    }
  ]
})

// 动态表单数据
const formData: Ref<Record<string, Record<string, any>>> = ref({})

// 正确处理el-tree多选，展示所有被勾选的叶子节点表单
const onTreeCheck = (_: any, treeObj: { checkedNodes: any[] }) => {
  const leafKeys: string[] = []
  treeObj.checkedNodes.forEach((node: any) => {
    if (!node.children || node.children.length === 0) {
      leafKeys.push(node.value)
    }
  })
  selectedTags.value = leafKeys
}

// 监听多选，自动初始化/清理表单数据
watch(selectedTags, (newVal) => {
  newVal.forEach(tag => {
    if (!formData.value[tag]) {
      const fields = tagFieldMap.value[tag] || []
      const obj: Record<string, any> = {}
      fields.forEach(field => {
        obj[field.code] = ''
      })
      formData.value[tag] = obj
    }
  })
  Object.keys(formData.value).forEach(tag => {
    if (!newVal.includes(tag)) {
      delete formData.value[tag]
    }
  })
}, { immediate: true })

const getTagLabel = (tag: string): string => {
  function findLabel(nodes: any[], value: string): string {
    for (const node of nodes) {
      if (node.value === value) return node.label
      if (node.children) {
        const found = findLabel(node.children, value)
        if (found) return found
      }
    }
    return value
  }
  return findLabel(treeData, tag)
}

const getFieldsByTag = (tag: string): FieldConfig[] => {
  return tagFieldMap.value[tag] || []
}

const getFieldOptions = (field: FieldConfig) => {
  // 这里可以根据字段类型返回对应的选项
  // 例如：下拉框、单选框、多选框的选项
  if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
    // 示例选项，实际应该从接口获取
    return [
      { label: '选项1', value: '1' },
      { label: '选项2', value: '2' },
      { label: '选项3', value: '3' }
    ]
  }
  if (field.fieldType === FieldType.REGION) {
    // 示例地区数据，实际应该从接口获取
    return [
      { label: '北京市', value: '110000', children: [
        { label: '北京市', value: '110100', children: [
          { label: '东城区', value: '110101' },
          { label: '西城区', value: '110102' }
        ]}
      ]}
    ]
  }
  return []
}

const open = (): void => { visible.value = true }
const setData = (data: { selectedTags?: string[], formData?: Record<string, any> }) => {
  if (data.selectedTags) selectedTags.value = [...data.selectedTags]
  if (data.formData) formData.value = { ...data.formData }
}
defineExpose({ open, setData })
const handleOk = (): void => {
  emit('ok', { selectedTags: selectedTags.value, formData: formData.value })
  visible.value = false
}
</script>