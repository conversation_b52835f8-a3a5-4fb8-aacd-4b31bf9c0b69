<template>
  <Dialog v-model="visible" width="53%" :close-on-click-modal="false" :destroy-on-close="true">
    <div class="flex gap-8 max-h-[50vh]">
      <!-- 左侧树形区块   人口多选，其余单选 -->
      <div
        class="w-72 min-w-60 bg-gray-50 border border-gray-200 rounded-lg p-4 flex-shrink-0 overflow-auto max-h-[50vh]"
      >
        <el-tree
          :data="treeData"
          node-key="value"
          :show-checkbox="labelType === 'people'"
          default-expand-all
          :props="defaultProps"
          :check-strictly="true"
          @check="onTreeCheck"
          @node-click="onNodeClick"
          ref="treeRef"
        />
      </div>

      <!-- 右侧动态表单 -->
      <div class="w-[900px] min-w-0 overflow-auto max-h-[50vh]">
        <el-space direction="vertical" :size="24" fill>
          <el-card
            v-for="(item, index) in formRenderObj"
            :key="index"
            shadow="hover"
            class="border border-gray-200 rounded-xl bg-white w-full"
            body-class="p-6 w-[600px]"
          >
            <div class="font-bold text-lg mb-4 text-gray-700">{{ getTagLabel(item) }}</div>
            <!-- 每个卡片有自己的表单 -->
            <el-form :ref="el => setFormRef(el, index)" :model="item.formData" :rules="item.formRule" label-width="auto">
              <template v-for="fieldGroup in item.formRenderData" :key="fieldGroup.id">
                <template v-for="field in fieldGroup.fields" :key="field.id">
                  <el-form-item :label="field.name" :prop="field.code" :required="field.required">
                    <SimpleFormField
                      :field="field"
                      v-model="item.formData[field.code]"
                      :field-options="getFieldOptions(field)"
                    />
                  </el-form-item>
                </template>
              </template>
            </el-form>
          </el-card>
        </el-space>
      </div>
    </div>
    <template #footer>
      <el-button @click="visible = false">上一步</el-button>
      <el-button type="primary" @click="handleOk">完成</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { type Ref } from 'vue'
import { FormInstance, FormRules } from 'element-plus'

// 定义表单数据结构
interface TagFormData {
  nodeId: string
  tagName: string
  formData: Record<string, any>
}
import SimpleFormField from '@/components/FormField/SimpleFormField.vue'
import * as LabelManageApi from '@/api/system/data/label-manage'
import { FieldType } from '@/config/constants/enums/field'
import type { LabelFieldConfig } from '@/api/system/data/label-manage'
import { handleTree2, defaultProps } from '@/utils/tree'
import { FormDataProcessor } from '@/utils/formDataProcessor'
import { useFormConfig } from '@/hooks/useFormConfig'
import { ViewFormType } from '@/config/constants/enums/label'
import { filterAndMarkGroups } from '@/utils/formatter'
import * as FieldConfApi from '@/api/system/data/field-conf'
import * as ViewFormConfApi from '@/api/system/data/view-form-conf'
import type { FieldConfig, FieldGroup, TagInfo } from '../types'

const emit = defineEmits<{
  ok: [data: { selectedTags: { id: string, name: string }[], formData: TagFormData[] }]
}>()
const route = useRoute()

// 使用 hooks
const {
  fieldOptionsMap,
  fetchFieldOptions,
  generateFormRules
} = useFormConfig()

const formRefs = ref<FormInstance[]>([])
// 是否显示
const visible = ref<boolean>(false)
// 树形组件ref
const treeRef = ref<any>()
// 树形标签数据
const treeData = ref<LabelFieldConfig[]>([])
// 选中的标签（叶子节点 value）
const selectedTags = ref<TagInfo[]>([])
// 动态表单数据（保留原有结构，但实际使用 formRenderObj 中的数据）
const formData: Ref<Record<string, Record<string, any>>> = ref({})
const labelType = ref<string>('') // 标签类型
const formRenderObj = ref<FormRenderItem[]>([])

// 设置表单引用
const setFormRef = (el: any, index: number): void => {
  if (el) {
    formRefs.value[index] = el
  }
}

// 正确处理el-tree多选/单选，展示所有被勾选的叶子节点表单
const onTreeCheck = (_: any, treeObj: { checkedNodes: any[] }): void => {
  const leafKeys: TagInfo[] = []
  treeObj.checkedNodes.forEach((node: any) => {
    if (!node.children || node.children.length === 0) {
      leafKeys.push({
        id: node.id,
        name: node.name
      })
    }
  })
  // 如果是单选，只保留最后一个
  if (labelType.value === 'people') {
    selectedTags.value = leafKeys
  } else {
    selectedTags.value = leafKeys.slice(-1)
  }

  // 清理旧的表单引用
  formRefs.value = []

  selectedTags.value.forEach((item) => {
    updateFormData(item.id)
  })
}



const updateFormData = async (id: string): Promise<void> => {
  try {
    // 如果存在就不调用了
    if (formRenderObj.value.find((item) => item.nodeId === id)) return

    // 通过 labelId 获取标签表单数据
    const { type = 'create' } = route.query
    const flagKey = type === 'create' ? 'addFlag' : 'editFlag'
    const manageId = '1942420981721182210'

    const fieldConfigs = await FieldConfApi.getFieldConfigListByManageId({
      manageId
    })
    const formConfData = await ViewFormConfApi.getViewFormConf({
      formType: type === 'create' ? ViewFormType.CREATE : ViewFormType.EDIT,
      manageId
    })

    const rawData = JSON.parse(formConfData.formJson)

    const filteredRes = fieldConfigs.filter((item: any) => item[flagKey])
    const allowedIds = filteredRes.map((item: any) => item.id)
    const filteredData: FieldGroup[] = filterAndMarkGroups(rawData, allowedIds)

    const formDataObj: Record<string, any> = {}
    if (filteredData && Array.isArray(filteredData)) {
      filteredData.forEach((group) => {
        group.fields.forEach((field: FieldConfig) => {
          // 修正字段扩展配置类型
          FormDataProcessor.normalizeFieldExtConfig(field)

          if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
            fetchFieldOptions(field)
          }
          // 初始化表单数据
          formDataObj[field.code] = ''
        })
      })

      // 生成验证规则
      const allFields = FormDataProcessor.normalizeFields(
        filteredData.flatMap((group: FieldGroup) => group.fields)
      )
      const formRule = generateFormRules(allFields)

      formRenderObj.value.push({
        formData: formDataObj,
        nodeId: id,
        formRule,
        formRenderData: filteredData
      })
    }
  } catch (error) {
    console.error('更新表单数据失败:', error)
    ElMessage.error('获取表单配置失败')
  }
}

// 单选时点击节点
const onNodeClick = async (node: any): Promise<void> => {
  console.log(node);

  const { id, name } = node

  selectedTags.value = [{
    id,
    name
  }]
  formRenderObj.value = []
  formRefs.value = [] // 清理表单引用
  await updateFormData(id)
}

const getTagLabel = (field: FormRenderItem): string => {
  const tag = selectedTags.value.find((tag) => tag.id === field.nodeId)
  return tag?.name || ''
}

const getFieldOptions = (field: FieldConfig): any[] => {
  if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
    return fieldOptionsMap.value.get(field.code) || []
  }
  return []
}

const open = async (newLabelType: string = 'people'): Promise<void> => {
  try {
    labelType.value = newLabelType
    visible.value = true
    // 获取标签管理数据
    const manageId = '1932725509586165761'
    const { list: listData } = await LabelManageApi.getLabelManagePage({
      pageNo: 1,
      pageSize: 100,
      labelId: manageId
    })
    treeData.value = handleTree2(Array.isArray(listData) ? listData : [listData])
  } catch (error) {
    console.error('获取标签数据失败:', error)
    ElMessage.error('获取标签数据失败')
  }
}

const setData = (data: { selectedTags?: TagInfo[]; formData?: Record<string, any> }): void => {
  if (data.selectedTags) selectedTags.value = [...data.selectedTags]
  if (data.formData) formData.value = { ...data.formData }
}

defineExpose({ open, setData })

const handleOk = async (): Promise<void> => {
  try {
    // 验证所有表单
    const validationPromises = formRefs.value.map(formRef => {
      return new Promise<boolean>((resolve) => {
        formRef?.validate((valid: boolean) => {
          resolve(valid)
        })
      })
    })

    const validationResults = await Promise.all(validationPromises)
    const allValid = validationResults.every(valid => valid)

    if (!allValid) {
      ElMessage.error('请检查表单填写是否正确')
      return
    }

    // 收集所有表单数据，保持数组结构避免key重复
    const allFormData: TagFormData[] = formRenderObj.value.map(item => ({
      nodeId: item.nodeId,
      tagName: selectedTags.value.find(tag => tag.id === item.nodeId)?.name || '',
      formData: item.formData
    }))

    console.log('表单数据', allFormData)

    emit('ok', {
      selectedTags: selectedTags.value,
      formData: allFormData
    })
    visible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('表单验证失败')
  }
}
</script>
