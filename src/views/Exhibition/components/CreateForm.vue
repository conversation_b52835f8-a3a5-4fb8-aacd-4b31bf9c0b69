<template>
  <Dialog v-model="dialogVisible" title="校验证件" width="30%" @close="handleClose">
    <el-form ref="fieldForm" :model="formData" label-width="100px" :rules="formRules">
      <!-- 循环出来的字段 -->
      <el-form-item label="证件类型" prop="type">
        <!-- 下拉框 -->
        <el-select
          v-if="dialogType === 'people'"
          v-model="formData.type"
          placeholder="请选择证件类型"
        >
          <el-option label="身份证" value="1" />
          <el-option label="护照" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="dialogType === 'people'" label="证件号码" prop="idCard">
        <!-- 输入框 -->
        <el-input v-model="formData.idCard" placeholder="请输入证件号码" />
      </el-form-item>
      <!-- 该人口未入库 / 该人口已入库 -->

      <el-form-item v-if="dialogType === 'house'" label="标准地址">
        <el-input v-model="formData.name" />
      </el-form-item>
      <el-form-item v-if="dialogType === 'house'" label="房号">
        <el-select v-model="formData.type" />
      </el-form-item>
      <!-- 会提示该房间已入库 /  该房屋未入库 -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <!-- 人口： 该人口未入库  添加（跳转新建）、暂不添加（关闭弹窗） -->
        <!-- 人口： 该人口已入库  编辑人口信息 （跳转编辑页面） -->
        <!-- 房屋：添加（跳转新建）、暂不添加（关闭弹窗）、 编辑房信息（编辑页面） -->
        <el-button @click="handleClose()">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 认</el-button>
      </span>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import * as BusinessDataApi from '@/api/system/business-data'
import { FormRules, FormInstance } from 'element-plus'
import { exhibitionFieldConfig } from '@/config/business'
import * as FieldConfApi from '@/api/system/data/field-conf'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'

// 表单数据接口
interface FormData {
  type: string // 证件类型
  idCard: string // 证件号码
  address: string // 标准地址
  roomId: string // 房号
  roomName: string // 房号名称
  name?: string // 名称
}

// 提交数据接口
interface SubmitData {
  type: string | undefined
  data: FormData
}

// API 检查响应接口
interface CheckResponse {
  valid: boolean
  message?: string
}

const emit = defineEmits<{
  submit: [data: SubmitData]
  noField: []
}>()

const dialogVisible = ref<boolean>(false) // 弹窗显示状态
const dialogType = ref<string | undefined>(undefined) // 弹窗类型
const fieldForm = ref<FormInstance>() // 表单引用
const isStored = ref<boolean>(false) // 是否入库
const formData = ref<FormData>({
  type: '', // 证件类型
  idCard: '', // 证件号码
  address: '', // 标准地址
  roomId: '', // 房号
  roomName: '', // 房号名称
  name: '' // 名称
}) // 表单数据

const validateIdCard = (_rule: any, value: string, callback: (error?: Error) => void): void => {
  if (!value) {
    return callback(new Error('请输入证件号码'))
  }
  // 这里调用你的接口
  apiCheckIdCard(value)
    .then((res: CheckResponse) => {
      if (res.valid) {
        callback()
        isStored.value = true
      } else {
        callback(new Error(res.message || '证件号码校验失败'))
        isStored.value = false
      }
    })
    .catch((_error: any) => {
      callback(new Error('校验接口异常'))
    })
}

async function apiCheckIdCard(idCard: string): Promise<CheckResponse> {
  const manageId = '1942420981721182210'
  return BusinessDataApi.addCheck({
    idCard,
    manageId
  })
}

const formRules = reactive<FormRules>({
  idCard: [
    { required: true, message: '请输入证件号码', trigger: 'blur' },
    { validator: validateIdCard, trigger: 'blur' }
  ]
})

// 重置表单
const resetForm = (): void => {
  fieldForm.value?.resetFields()
}

// 关闭弹窗
const handleClose = (): void => {
  resetForm()
  dialogVisible.value = false
}

const handleSubmit = (): void => {
  fieldForm.value?.validate((valid: boolean) => {
    if (valid) {
      console.log('表单数据', formData.value)
      emit('submit', {
        type: dialogType.value,
        data: formData.value
      })
    } else {
      console.log('表单验证失败')
    }
  })
}

// 打开弹窗（支持回显）
const open = async (): Promise<void> => {
  try {
    const manageId = '1942420981721182210'
    const fieldConfigs = await FieldConfApi.getFieldConfigListByManageId({ manageId })
    let displayFields: LabelFieldConfig[] = []

    for (const config of exhibitionFieldConfig) {
      // config.fields 是要展示的字段 code 数组
      const matchedFields = config.fields
        .map(fieldCode => fieldConfigs.find(f => f.code === fieldCode))
        .filter(Boolean) as LabelFieldConfig[]

      if (matchedFields.length > 0) {
        dialogType.value = config.key as string
        displayFields = matchedFields
        break
      }
    }

    console.log('要展示的字段', displayFields)
    if (displayFields.length > 0) {
      // 渲染表单
      dialogVisible.value = true
    } else {
      emit('noField')
    }
  } catch (error) {
    console.error('获取字段配置失败:', error)
    ElMessage.error('获取字段配置失败')
  }
}

defineExpose({ open })
</script>
