<template>
  <ContentWrap>
    <!-- <el-check-tag checked>Checked</el-check-tag> -->
    <div class="flex justify-between gap-2 mt-2">
      <el-card shadow="never" class="w-5/6">
        <template #header>
          <div class="flex justify-between">
            <span>详情</span>
            <el-popover :visible="popoverVisible" :width="400" trigger="click">
              <template #reference>
                <el-button type="primary" @click="showSensitivePassword" :loading="passwordLoading">输入敏感密码</el-button>
              </template>
              <el-form :model="form" label-width="auto" style="max-width: 600px">
                <el-form-item label="密码">
                  <el-input v-model="form.password" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="checkSensitivePassword" :loading="passwordLoading">验证</el-button>
                </el-form-item>
              </el-form>
            </el-popover>
          </div>
        </template>
        <el-form ref="formRef" :model="formData" label-width="auto">
          <template v-for="fieldGroup in fieldGroups" :key="fieldGroup.id">
            <el-row :gutter="20">
              <el-col
                v-for="field in fieldGroup.fields"
                :key="field.id"
                :span="getFieldSpan(fieldGroup)"
                v-show="isFieldVisibleWrapper(field)"
              >
                <el-form-item :label="field.name">
                  <!-- 图片用 -->
                  {{ genterFormText(field) }}
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </el-form>
      </el-card>
      <el-card shadow="never" class="w-1/6" v-if="tabConfigs.length > 1">
        <div class="flex flex-col mb-4" v-for="tab in tabConfigs" :key="tab.value">
          <!-- 点击出现弹窗，弹窗显示对应 显示该人口数据的基础和业务标签（最后层级）
                点击标签，弹窗展示该标签对应的详情
                当只存在一个标签时候，不显示该标签栏 -->
          <el-button
            :type="activeTab === tab.value ? 'primary' : 'info'"
            @click="activeTab = tab.value"
            block
          >
            {{ tab.label }}
          </el-button>
        </div>
      </el-card>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import * as BusinessDataApi from '@/api/system/business-data'
import * as ViewFormConfApi from '@/api/system/data/view-form-conf'
import { filterAndMarkGroups, getFieldSpan } from '@/utils/formatter'
import { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'
import { FormRow } from '@/hooks/web/useFormEditHandlers'
import { BooleanEnum } from '@/config/constants/enums/label'
import * as FieldConfApi from '@/api/system/data/field-conf'
import { useFormConfig } from '@/hooks/useFormConfig'
import { useFieldVisibility } from '@/hooks/useFieldVisibility'
import { useFormTextGenerator } from '@/hooks/useFormTextGenerator'

defineOptions({ name: 'ExhibitionDetail' })

const route = useRoute()

// 使用 hooks
const { formData, fieldGroups, fieldOptionsMap, fetchFieldOptions } = useFormConfig()
const { isFieldVisible } = useFieldVisibility()
const { generateFormText } = useFormTextGenerator()

const formRows = ref<FormRow[]>([])
const tabConfigs = ref<any[]>([]) // 标签展示数据
const activeTab = ref<string | undefined>(undefined)
const popoverVisible = ref(false)
const form = ref({
  password: ''
})
const passwordLoading = ref(false)

const genterFormText = (field: LabelFieldConfig) => {
  return generateFormText(field, formData.value, fieldOptionsMap.value)
}

const showSensitivePassword = () => {
  popoverVisible.value = true
}

const setLayoutData = (data: FormRow[]) => {
  if (!data || !Array.isArray(data)) {
    console.error('加载布局失败：提供了无效的数据。')
    return
  }
  formRows.value = JSON.parse(JSON.stringify(data))
  let maxId = 0
  data.forEach((row) => {
    const rowIdNum = parseInt(row.id.split('-')[1])
    if (rowIdNum > maxId) maxId = rowIdNum
    row.fields.forEach((field) => {
      const fieldIdNum = parseInt(field.id.split('-')[1])
      if (fieldIdNum > maxId) maxId = fieldIdNum
    })
  })
}

// 包装 isFieldVisible 函数以适配当前组件的数据结构
const isFieldVisibleWrapper = (field: any) => {
  return isFieldVisible(field, fieldGroups.value, formData.value)
}



const checkSensitivePassword = () => {
  passwordLoading.value = true
  // const manageId = (route.meta.manageId as string) || '1942420981721182210'
  const manageId = '1942420981721182210'
  const businessId = route.query.id as string
  BusinessDataApi.getBusinessData({ id: businessId, manageId, sensitivePwd: form.value.password }).then((res) => {
    popoverVisible.value = false
    formData.value = res
        // 填充业务数据
    formRows.value.forEach((group: any) => {
      if (group.fields?.length) {
        group.fields.forEach((field: any) => {
          if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
            fetchFieldOptions(field)
          }
          const businessValue = res[field.code]
          if (businessValue !== undefined) {
            field.value = businessValue
          }
        })
      }
    })

  })
  .catch(() => {
    ElMessage.error('密码错误')
  })
  .finally(() => {
    passwordLoading.value = false
  })
}

// 获取对应标签的详情配置接口
const getDetailConfig = async () => {
  try {
    // TODO: 从路由或配置中获取 manageId
    // const manageId = (route.meta.manageId as string) || '1942420981721182210'
    const manageId = '1942420981721182210'
    const businessId = route.query.id as string

    if (!businessId) {
      ElMessage.error('缺少业务数据ID')
      return
    }

    // 并行获取数据，提高性能
    const [fieldConfigs, formConfData, businessData] = await Promise.all([
      FieldConfApi.getFieldConfigListByManageId({ manageId }),
      ViewFormConfApi.getViewFormConf({ manageId, formType: 2 }),
      BusinessDataApi.getBusinessData({ id: businessId, manageId })
    ])

    if (!formConfData) {
      ElMessage.warning('未找到表单配置数据')
      return
    }

    formData.value = businessData

    if (!formConfData.formJson) {
      return
    }

    // 解析表单配置
    const rawData = JSON.parse(formConfData.formJson)
    const allowedIds = fieldConfigs
      .filter((config) => config.pcViewFlag === BooleanEnum.TRUE && config.id)
      .map((config) => config.id!)

    const filteredData = filterAndMarkGroups(rawData, allowedIds)

    // 填充业务数据
    filteredData.forEach((group: any) => {
      if (group.fields?.length) {
        group.fields.forEach((field: any) => {
          if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
            fetchFieldOptions(field)
          }
          if (field.fieldType === FieldType.DATE_RANGE) {
            const code2 = field.fieldConfExtDOList.find((item: any) => item.name === 'code2')?.value
            field.value = [businessData[field.code], businessData[code2]]
          } else {
          const businessValue = businessData[field.code]
            if (businessValue !== undefined) {
              field.value = businessValue
            }
          }
        })
      }
    })

    fieldGroups.value = filteredData

    setLayoutData(filteredData as unknown as FormRow[])
  } catch (error) {
    console.error('获取详情配置失败:', error)
    ElMessage.error('获取详情配置失败')
  }
}
// 生命周期钩子
onMounted(() => {
  getDetailConfig()
})
</script>
