<template>
  <ContentWrap>
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="auto">
      <template v-for="fieldGroup in fieldGroups" :key="fieldGroup.id">
        <el-row :gutter="20">
          <el-col
            v-for="field in fieldGroup.fields"
            :key="field.id"
            :span="getFieldSpan(fieldGroup)"
            v-show="isFieldVisible(field)"
          >
            <el-form-item :label="field.name" :prop="field.code" :required="field.required">
              <SimpleFormField
                :field="field"
                v-model="formData[field.code]"
                :field-options="getFieldOptions(field)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-row :gutter="20">
        <el-col :span="12" :offset="6">
          <el-form-item >
              <el-button v-show="editType === 'create'" type="primary" size="large" @click="handleSelectTag">{{ !labelType ? '下一步' : '选择标签' }}</el-button>
              <el-button v-show="editType === 'edit'" type="primary" size="large" @click="handleSelectTag">{{ !labelType ? '下一步' : '选择标签' }}</el-button>
              <el-button v-show="editType === 'create'" type="primary" size="large" @click="handleSubmit">{{ !labelType ? '添加' : '添加' }}</el-button>
              <el-button v-show="editType === 'edit'" type="primary" size="large" @click="handleSubmit">{{ !labelType ? '完成' : '添加' }}</el-button>
              <!-- 调接口看看有没有业务标签，有的话就展示 -->
              <!-- <el-button type="primary" @click="handleSelectTag">选择标签</el-button> -->
              <!-- <el-button type="primary" @click="setSelectTag">设置标签</el-button> -->
          </el-form-item>
        </el-col>
    </el-row>

    </el-form>
    <TagMultiForm ref="tagMultiFormRef" @ok="onTagOk"  />
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({ name: 'ExhibitionCreate' })
import * as DictDataApi from '@/api/system/dict/dict.data'
import * as BusinessDataApi from '@/api/system/business-data'
import * as FieldConfApi from '@/api/system/data/field-conf'
import * as ViewFormConfApi from '@/api/system/data/view-form-conf'
import { FormRules } from 'element-plus'
import {
  validatePatternMapNumber,
  validateUSCC,
  createRequiredRule,
  createRegexRule
} from '@/utils/formRules'
import { filterAndMarkGroups, getFieldSpan } from '@/utils/formatter'
import { FieldType } from '@/config/constants/enums/field'
import TagMultiForm from './components/TagMultiForm.vue'
import { ViewFormType } from '@/config/constants/enums/label'
import { exhibitionFieldConfig } from '@/config/exhibitionFieldConfig'
import { FormDataProcessor, LabelConfigProcessor, FormConfigProcessor } from '@/utils/formDataProcessor'

const route = useRoute()
const router = useRouter()

const formRef = ref()
const fieldGroups = ref<any[]>([])
const formData = ref<any>({})
const editType = ref<('create' | 'edit')>('create') // 是否是编辑
const formRules = reactive<FormRules>({})
const fieldOptionsMap = ref(new Map<string, any[]>()) // 字段选项
const labelType = ref('') // 标签类型 人口、场所、物地、一标三实、无
const creatDialogFormRender = ref<any[]>([]) // 创建弹窗表单渲染
const tagMultiFormRef = ref() // 弹窗控制







// 获取字段选项
const fetchFieldOptions = async (field: any) => {
  const { list } = await DictDataApi.getDictDataPage({
    pageNo: 1,
    pageSize: 10,
    dictType: field.fieldConfExtDOList[0].value
  })
  fieldOptionsMap.value.set(field.code, list)
  // 其他类型可按需扩展
}

// 判断字段是否应该显示 todo zhaokun 这里需要测试
const isFieldVisible = (field: any) => {
  const linkage = field.linkage
  if (!linkage || !linkage.enabled) {
    return true // 没有关联配置或未启用，默认显示
  }

  const targetFieldId = linkage.targetFieldId
  const targetFieldValue = linkage.targetFieldValue
  const condition = linkage.condition

  if (!targetFieldId) {
    return true
  }

  // 找到目标字段的值
  let targetValue = null
  for (const group of fieldGroups.value) {
    for (const f of group.fields) {
      if (f.id === targetFieldId) {
        targetValue = formData.value[f.code]
        break
      }
    }
    if (targetValue !== null) break
  }

  // 根据条件判断是否显示
  switch (condition) {
    case 'equals':
      return targetValue === targetFieldValue
    case 'notEquals':
      return targetValue !== targetFieldValue
    case 'contains':
    case 'notContains':
    case 'startsWith':
    case 'endsWith':
      return String(targetValue).includes(String(targetFieldValue))
    default:
      return true
  }
}

// 生成表单验证规则
const generateFormRules = (fields: any[]): FormRules => {
  return fields.reduce((rules: FormRules, field) => {
    let ruleArr: any[] = []

    // 必填验证
    if (field.required) ruleArr.push(createRequiredRule(field.name))

    // 自定义正则验证
    if (field.fieldType === 1) {
      const { required, fieldConfExtObj } = field
      const { regex, prompt = '格式不正确', dataValidation } = fieldConfExtObj
      if (dataValidation === '1' && regex) {
        ruleArr.push(createRegexRule(regex, prompt, required))
      } else if (dataValidation === '3') {
        ruleArr.push({
          validator: (_rule, value, callback) => {
            if (!required && !value) return callback()
            if (!validateUSCC(value)) {
              callback(new Error(prompt))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        })
      } else {
        const regex = validatePatternMapNumber[dataValidation]
        ruleArr.push(createRegexRule(regex, prompt, required))
      }
    }
    if (ruleArr.length) rules[field.code] = ruleArr
    return rules
  }, {})
}

// 处理提交
const handleSubmit = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const manageId = '1942420981721182210'
      const businessJson = FormDataProcessor.processSubmitData(fieldGroups.value, formData.value)

      try {
        if (route.query.editType && route.query.id) {
          await BusinessDataApi.updateBusinessData({
            businessJson,
            id: route.query.id as string,
            manageId
          });
          ElMessage.success('更新成功');
        } else {
          await BusinessDataApi.createBusinessData({
            businessJson,
            manageId
          });
          ElMessage.success('添加成功')
        }
      } catch (err) {
        console.error(err);
        ElMessage.error(route.query.editType ? '更新失败' : '添加失败');
      }
    } else {
      console.error('表单验证失败')
    }
  })
}

const handleSelectTag = () => {
  tagMultiFormRef.value.open()
}

const onTagOk = (data: any) => {
  // 这里可以拿到弹窗返回的标签和表单数据
  console.log('标签选择确定', data)
}

// 初始化字段选项（单选框、复选框）
const initFieldOptions = (field: any) => {
  if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
    fetchFieldOptions(field)
  }
}

// 处理字段数据初始化
const processFieldsData = (filteredData: any[], businessData: any) => {
  // 重置表单数据
  formData.value = {}

  filteredData.forEach((group) => {
    group.fields.forEach((field: any) => {
      // 修正字段扩展配置类型
      FormDataProcessor.normalizeFieldExtConfig(field)

      // 初始化字段选项
      initFieldOptions(field)

      // 初始化表单数据
      FormDataProcessor.initFieldFormData(field, businessData, formData.value)
    })
  })

  // 设置字段组
  fieldGroups.value = filteredData as unknown as any

  // 生成验证规则
  const allFields = FormDataProcessor.normalizeFields(
    filteredData.flatMap((group: any) => group.fields)
  )
  Object.assign(formRules, generateFormRules(allFields))
}

// 初始化标签类型
const initLabelType = (fieldConfigs: any[]) => {
  const result = LabelConfigProcessor.findMatchingLabelType(exhibitionFieldConfig, fieldConfigs)
  if (result) {
    labelType.value = result.labelType
    creatDialogFormRender.value = result.matchedFields
  }
}

// 获取基础配置信息
const getBaseConfig = () => {
  const manageId = '1942420981721182210'
  const { type = 'create', id } = route.query

  editType.value = type as ('create' | 'edit')
  const formType = type === 'create' ? ViewFormType.CREATE : ViewFormType.EDIT
  const flagKey = type === 'create' ? 'addFlag' : 'editFlag'

  return { manageId, type: type as string, id: id as string | null, formType, flagKey }
}

// 获取字段配置和业务数据
const fetchConfigAndBusinessData = async (manageId: string, type: string, id: string | null) => {
  const fieldConfigs = await FieldConfApi.getFieldConfigListByManageId({ manageId })

  let businessData = {}
  if (type === 'edit' && id) {
    businessData = await BusinessDataApi.getBusinessData({
      id,
      manageId
    })
  }

  return { fieldConfigs, businessData }
}

// 获取表单配置数据
const getFormConfigData = async (manageId: string, formType: number, flagKey: string, fieldConfigs: any[]) => {
  const filteredRes = FormConfigProcessor.getFilteredFieldConfigs(fieldConfigs, flagKey)
  const formConfData = await ViewFormConfApi.getViewFormConf({
    manageId,
    formType
  })

  const rawData = FormConfigProcessor.parseFormConfig(formConfData)
  const allowedIds = FormConfigProcessor.getAllowedIds(filteredRes)
  const filteredData = filterAndMarkGroups(rawData, allowedIds)

  return filteredData
}

const init = async () => {
  try {
    // 获取基础配置
    const { manageId, type, id, formType, flagKey } = getBaseConfig()

    // 获取字段配置和业务数据
    const { fieldConfigs, businessData } = await fetchConfigAndBusinessData(manageId, type, id)

    // 获取表单配置数据
    const filteredData = await getFormConfigData(manageId, formType, flagKey, fieldConfigs)

    // 处理字段数据
    if (filteredData && Array.isArray(filteredData)) {
      processFieldsData(filteredData, businessData)
    }

    // 初始化标签类型
    initLabelType(fieldConfigs)

  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('页面初始化失败')
  }
}


const getFieldOptions = (field: any): any[] => {
  if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
    return fieldOptionsMap.value.get(field.code) || []
  }
  return []
}

onMounted(() => {
  init()
})
</script>