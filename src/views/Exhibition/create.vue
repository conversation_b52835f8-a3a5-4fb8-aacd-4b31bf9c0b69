<template>
  <ContentWrap>
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="auto">
      <template v-for="fieldGroup in fieldGroups" :key="fieldGroup.id">
        <el-row :gutter="20">
          <el-col
            v-for="field in fieldGroup.fields"
            :key="field.id"
            :span="getFieldSpan(fieldGroup)"
            v-show="isFieldVisibleWrapper(field)"
          >
            <el-form-item :label="field.name" :prop="field.code" :required="field.required">
              <SimpleFormField
                :field="field"
                v-model="formData[field.code]"
                :field-options="getFieldOptions(field)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-row :gutter="20">
        <el-col :span="12" :offset="6">
          <el-form-item >
              <el-button v-show="editType === 'create'" type="primary" size="large" @click="handleSelectTag">{{ !labelType ? '下一步' : '选择标签' }}</el-button>
              <el-button v-show="editType === 'edit'" type="primary" size="large" @click="handleSelectTag">{{ !labelType ? '下一步' : '选择标签' }}</el-button>
              <el-button v-show="editType === 'create'" type="primary" size="large" @click="handleSubmit">{{ !labelType ? '添加' : '添加' }}</el-button>
              <el-button v-show="editType === 'edit'" type="primary" size="large" @click="handleSubmit">{{ !labelType ? '完成' : '添加' }}</el-button>
              <!-- 调接口看看有没有业务标签，有的话就展示 -->
              <!-- <el-button type="primary" @click="handleSelectTag">选择标签</el-button> -->
              <!-- <el-button type="primary" @click="setSelectTag">设置标签</el-button> -->
          </el-form-item>
        </el-col>
    </el-row>

    </el-form>
    <TagMultiForm ref="tagMultiFormRef" @ok="onTagOk"  />
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({ name: 'ExhibitionCreate' })
import * as BusinessDataApi from '@/api/system/business-data'
import { getFieldSpan } from '@/utils/formatter'
import TagMultiForm from './components/TagMultiForm.vue'
import { exhibitionFieldConfig } from '@/config/business'
import { FormDataProcessor, LabelConfigProcessor } from '@/utils/formDataProcessor'
import { useFormConfig } from '@/hooks/useFormConfig'
import { useFieldVisibility } from '@/hooks/useFieldVisibility'
import type { FieldConfig, TagInfo } from '@/config/constants/types'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'

const route = useRoute()

// 使用 hooks
const {
  fieldGroups,
  formData,
  formRules,
  getFieldOptions,
  getBaseConfig,
  fetchConfigAndBusinessData,
  getFormConfigData,
  processFieldsData
} = useFormConfig()

const { isFieldVisible } = useFieldVisibility()

const formRef = ref<any>()
const editType = ref<'create' | 'edit'>('create') // 是否是编辑
const labelType = ref<string>('') // 标签类型 人口、场所、物地、一标三实、无
const creatDialogFormRender = ref<LabelFieldConfig[]>([]) // 创建弹窗表单渲染
const tagMultiFormRef = ref<any>() // 弹窗控制

// 包装 isFieldVisible 函数以适配当前组件的数据结构
const isFieldVisibleWrapper = (field: LabelFieldConfig): boolean => {
  return isFieldVisible(field as any, fieldGroups.value, formData.value)
}

// 处理提交
const handleSubmit = async (): Promise<void> => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const manageId = '1942420981721182210'
      const businessJson = FormDataProcessor.processSubmitData(fieldGroups.value, formData.value)

      try {
        if (route.query.editType && route.query.id) {
          await BusinessDataApi.updateBusinessData({
            businessJson,
            id: route.query.id as string,
            manageId
          });
          ElMessage.success('更新成功');
        } else {
          await BusinessDataApi.createBusinessData({
            businessJson,
            manageId
          });
          ElMessage.success('添加成功')
        }
      } catch (error) {
        console.error('提交失败:', error);
        ElMessage.error(route.query.editType ? '更新失败' : '添加失败');
      }
    } else {
      console.error('表单验证失败')
    }
  })
}

const handleSelectTag = (): void => {
  tagMultiFormRef.value.open()
}

const onTagOk = (data: { selectedTags: TagInfo[], formData: any }): void => {
  // 这里可以拿到弹窗返回的标签和表单数据
  console.log('标签选择确定', data)
}

// 初始化标签类型
const initLabelType = (fieldConfigs: LabelFieldConfig[]): void => {
  const result = LabelConfigProcessor.findMatchingLabelType(exhibitionFieldConfig, fieldConfigs)
  if (result) {
    labelType.value = result.labelType
    creatDialogFormRender.value = result.matchedFields
  }
}

const init = async (): Promise<void> => {
  try {
    // 获取基础配置
    const { manageId, type, id, formType, flagKey } = getBaseConfig(route, editType)

    // 获取字段配置和业务数据
    const { fieldConfigs, businessData } = await fetchConfigAndBusinessData(manageId, type, id)

    // 获取表单配置数据
    const filteredData = await getFormConfigData(manageId, formType, flagKey, fieldConfigs)

    // 处理字段数据
    if (filteredData && Array.isArray(filteredData)) {
      processFieldsData(filteredData, businessData)
    }

    // 初始化标签类型
    initLabelType(fieldConfigs)

  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('页面初始化失败')
  }
}

onMounted(() => {
  init()
})
</script>