import { FieldType } from '@/config/constants/enums/field'
import type { FieldGroup } from '@/views/Exhibition/types'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'

/**
 * 表单数据处理工具类
 */
export class FormDataProcessor {
  /**
   * 修正字段扩展配置的类型
   */
  static normalizeFieldExtConfig(field: LabelFieldConfig): void {
    if (Array.isArray(field.fieldConfExtDOList)) {
      field.fieldConfExtDOList.forEach((item: any) => {
        item.type = item.type == null ? 0 : Number(item.type)
      })
    }
  }

  /**
   * 根据字段类型初始化表单数据
   */
  static initFieldFormData(field: LabelFieldConfig, businessData: Record<string, any>, formData: Record<string, any>): void {
    switch (field.fieldType) {
      case FieldType.CHECKBOX:
        formData[field.code] = businessData[field.code]
          ? String(businessData[field.code]).split(',')
          : []
        break
      case FieldType.RADIO:
        formData[field.code] = businessData[field.code]?.toString() || ''
        break
      case FieldType.DATE_RANGE:
        const code2 = field?.fieldConfExtDOList?.find((item: any) => item.name === 'code2')?.value || ''
        formData[code2] = businessData[code2] || ''
        formData[field.code] = [businessData[field.code], businessData[code2]]
        break
      default:
        formData[field.code] = businessData[field.code] || ''
    }
  }

  /**
   * 处理提交时的表单数据转换
   */
  static processSubmitData(fieldGroups: FieldGroup[], formData: Record<string, any>): Record<string, any> {
    const businessJson: Record<string, any> = {}

    fieldGroups.forEach((group) => {
      group.fields.forEach((field: LabelFieldConfig) => {
        if (field.fieldType === FieldType.DATE_RANGE) {
          const val = formData[field.code]
          if (Array.isArray(val) && val.length === 2) {
            businessJson[field.code] = val[0]
            const code2 = field.fieldConfExtDOList.find((item: any) => item.name === 'code2')?.value
            if (code2) {
              businessJson[code2] = val[1]
            }
          }
        } else if (field.fieldType === FieldType.CHECKBOX) {
          businessJson[field.code] = Array.isArray(formData[field.code])
            ? formData[field.code].join(',')
            : ''
        } else {
          businessJson[field.code] = formData[field.code]
        }
      })
    })

    return businessJson
  }

  /**
   * 标准化字段配置
   */
  static normalizeFields(fields: any[]): any[] {
    return fields.map((field: any) => ({
      ...field,
      fieldConfExtDOList:
        field.fieldConfExtDOList?.map((item: any) => ({
          ...item,
          type: item.type == null ? 0 : Number(item.type)
        })) || []
    }))
  }
}

/**
 * 标签配置处理工具
 */
export class LabelConfigProcessor {
  /**
   * 根据字段配置查找匹配的标签类型
   */
  static findMatchingLabelType(exhibitionFieldConfig: any[], fieldConfigs: any[]): {
    labelType: string
    matchedFields: any[]
  } | null {
    for (const config of exhibitionFieldConfig) {
      const matchedFields = config.fields
        .map((fieldCode: string) => fieldConfigs.find(f => f.code === fieldCode))
        .filter(Boolean)

      if (matchedFields.length > 0) {
        return {
          labelType: config.key as string,
          matchedFields
        }
      }
    }
    return null
  }
}

/**
 * 表单配置处理工具
 */
export class FormConfigProcessor {
  /**
   * 获取过滤后的字段配置
   */
  static getFilteredFieldConfigs(fieldConfigs: any[], flagKey: string): any[] {
    return fieldConfigs.filter((item: any) => item[flagKey])
  }

  /**
   * 解析表单JSON配置
   */
  static parseFormConfig(formConfData: any): any {
    return JSON.parse(formConfData.formJson)
  }

  /**
   * 获取允许的字段ID列表
   */
  static getAllowedIds(filteredRes: any[]): any[] {
    return filteredRes.map((item: any) => item.id)
  }
}
