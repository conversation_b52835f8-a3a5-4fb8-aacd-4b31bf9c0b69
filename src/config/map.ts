/**
 * 地图相关配置
 */

// 高德地图配置
export const amapConfig = {
  // 从环境变量获取API密钥
  key: import.meta.env.VITE_AMAP_KEY || '',

  // 高德地图版本
  version: '2.0',

  // 需要加载的插件
  plugins: [
    'AMap.Geocoder',        // 地理编码
    'AMap.AutoComplete',    // 自动完成
    'AMap.PlaceSearch',     // 地点搜索
    'AMap.DistrictSearch',  // 行政区查询
    'AMap.Geolocation',     // 定位
    'AMap.CitySearch',      // 城市查询
    'AMap.Scale',           // 比例尺
    'AMap.ToolBar',         // 工具条
    'AMap.MouseTool',       // 鼠标工具
    'AMap.PolyEditor',      // 多边形编辑
    'AMap.CircleEditor'     // 圆形编辑
  ],

  // 默认地图配置
  defaultOptions: {
    center: [120.088, 30.86], // 默认中心点（湖州）
    zoom: 12,                 // 默认缩放级别
    mapStyle: 'amap://styles/normal', // 地图样式
    resizeEnable: true,       // 是否监控地图容器尺寸变化
    rotateEnable: false,      // 是否允许旋转
    pitchEnable: false,       // 是否允许倾斜
    zoomEnable: true,         // 是否允许缩放
    dragEnable: true,         // 是否允许拖拽
    keyboardEnable: true,     // 是否允许键盘控制
    doubleClickZoom: true,    // 是否允许双击缩放
    scrollWheel: true,        // 是否允许滚轮缩放
    touchZoom: true,          // 是否允许触摸缩放
    showIndoorMap: false,     // 是否显示室内地图
    expandZoomRange: true,    // 是否支持可以扩展最大缩放级别
    zooms: [3, 20],          // 地图显示的缩放级别范围
    showBuildingBlock: true,  // 是否显示3D楼块
    viewMode: '2D' as const,  // 地图模式
    features: ['bg', 'point', 'road', 'building'], // 地图上显示的元素种类
    showLabel: true           // 是否显示地图文字标记
  },

  // 地图样式配置
  styles: {
    normal: 'amap://styles/normal',           // 标准
    dark: 'amap://styles/dark',               // 幻影黑
    light: 'amap://styles/light',             // 月光银
    fresh: 'amap://styles/fresh',             // 草色青
    grey: 'amap://styles/grey',               // 雅士灰
    graffiti: 'amap://styles/graffiti',       // 涂鸦
    macaron: 'amap://styles/macaron',         // 马卡龙
    blue: 'amap://styles/blue',               // 靛青蓝
    darkblue: 'amap://styles/darkblue',       // 极夜蓝
    wine: 'amap://styles/wine'                // 酱籽
  }
} as const

// 地理编码配置
export const geocodingConfig = {
  // 地理编码服务配置
  geocoder: {
    city: '湖州市',           // 默认城市
    radius: 1000,            // 搜索半径（米）
    extensions: 'all'        // 返回信息详略
  },

  // 逆地理编码配置
  regeocode: {
    radius: 1000,            // 搜索半径（米）
    extensions: 'all',       // 返回信息详略
    batch: false,            // 是否批量查询
    roadLevel: 1             // 道路等级
  }
} as const

// 搜索配置
export const searchConfig = {
  // 地点搜索配置
  placeSearch: {
    pageSize: 10,            // 每页结果数
    pageIndex: 1,            // 页码
    city: '湖州市',          // 搜索城市
    citylimit: true,         // 是否限制在设置的城市内搜索
    map: null,               // 地图实例
    panel: null,             // 结果列表容器
    showCover: true,         // 是否显示覆盖物
    renderStyle: 'newpc'     // 渲染风格
  },

  // 自动完成配置
  autoComplete: {
    city: '湖州市',          // 搜索城市
    citylimit: true,         // 是否限制在设置的城市内搜索
    datatype: 'all',         // 返回数据类型
    input: ''                // 输入框ID
  }
} as const

// 定位配置
export const locationConfig = {
  // 浏览器定位配置
  geolocation: {
    enableHighAccuracy: true,  // 是否使用高精度定位
    timeout: 10000,           // 超时时间（毫秒）
    maximumAge: 0,            // 定位结果缓存时间（毫秒）
    convert: true,            // 是否进行坐标偏移
    showButton: true,         // 是否显示定位按钮
    buttonDom: null,          // 自定义定位按钮
    showMarker: true,         // 是否显示定位标记
    showCircle: true,         // 是否显示精度圆
    panToLocation: true,      // 是否将定位结果作为地图中心点
    zoomToAccuracy: true      // 是否根据定位精度调整地图缩放级别
  }
} as const

// 绘制工具配置
export const drawingConfig = {
  // 鼠标工具配置
  mouseTool: {
    strokeColor: '#FF33FF',   // 线条颜色
    strokeOpacity: 0.8,       // 线条透明度
    strokeWeight: 2,          // 线条宽度
    fillColor: '#1791fc',     // 填充颜色
    fillOpacity: 0.35,        // 填充透明度
    strokeStyle: 'solid',     // 线条样式
    strokeDasharray: [10, 5]  // 虚线样式
  },

  // 编辑器配置
  editor: {
    strokeColor: '#FF0000',   // 编辑时线条颜色
    strokeOpacity: 1,         // 编辑时线条透明度
    strokeWeight: 2,          // 编辑时线条宽度
    fillColor: '#00FF00',     // 编辑时填充颜色
    fillOpacity: 0.5          // 编辑时填充透明度
  }
} as const

// 标记配置
export const markerConfig = {
  // 默认标记样式
  default: {
    icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
    size: [19, 33],           // 图标大小
    offset: [-9, -33],        // 图标偏移
    anchor: 'bottom-center'   // 锚点位置
  },

  // 自定义标记样式
  custom: {
    size: [32, 32],
    offset: [-16, -32],
    anchor: 'bottom-center'
  },

  // 聚合标记配置
  cluster: {
    gridSize: 80,             // 聚合计算时网格的像素大小
    maxZoom: 14,              // 最大的聚合级别
    averageCenter: true,      // 聚合点的落脚位置是否是所有聚合在内点的平均中心
    zoomOnClick: true,        // 点击聚合点时，是否散开
    styles: [
      {
        url: 'https://a.amap.com/jsapi_demos/static/cluster_style.png',
        size: [53, 53],
        offset: [-26, -26]
      }
    ]
  }
} as const

// 信息窗体配置
export const infoWindowConfig = {
  // 默认配置
  default: {
    isCustom: false,          // 是否自定义窗体
    autoMove: true,           // 是否自动调整窗体到视野内
    closeWhenClickMap: true,  // 点击地图时是否关闭
    size: [200, 100],         // 信息窗体大小
    offset: [0, -30],         // 信息窗体偏移
    showShadow: true          // 是否显示阴影
  },

  // 自定义样式
  custom: {
    isCustom: true,
    autoMove: true,
    closeWhenClickMap: true,
    offset: [0, -30]
  }
} as const

// 验证API密钥是否配置
export const validateAmapKey = (): boolean => {
  if (!amapConfig.key) {
    console.error('高德地图API密钥未配置，请在环境变量中设置 VITE_AMAP_KEY')
    return false
  }
  return true
}

// 获取高德地图API密钥
export const getAmapKey = (): string => {
  if (!validateAmapKey()) {
    throw new Error('高德地图API密钥未配置')
  }
  return amapConfig.key
}

// 地图工具函数
export const mapUtils = {
  /**
   * 格式化坐标显示
   */
  formatCoordinate: (lng: number, lat: number, precision = 6): string => {
    return `${lng.toFixed(precision)}, ${lat.toFixed(precision)}`
  },

  /**
   * 验证坐标是否有效
   */
  isValidCoordinate: (lng: number, lat: number): boolean => {
    return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90
  },

  /**
   * 计算两点之间的距离（简单计算）
   */
  getDistance: (lng1: number, lat1: number, lng2: number, lat2: number): number => {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }
}
