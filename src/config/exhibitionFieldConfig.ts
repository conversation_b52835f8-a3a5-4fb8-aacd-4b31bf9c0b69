// src/config/exhibitionFieldConfig.ts
export interface ExhibitionFieldConfig {
  key: string
  label: string
  fields: string[]
}

export const exhibitionFieldConfig: ExhibitionFieldConfig[] = [
  {
    key: 'person',
    label: '人口',
    fields: ['证件类型', '证件号码']
  },
  {
    key: 'object',
    label: '物帝',
    fields: ['编码']
  },
  {
    key: 'place',
    label: '场所',
    fields: ['统一信用代码']
  },
  {
    key: 'ybss',
    label: '一标三实',
    fields: ['编码']
  }
]