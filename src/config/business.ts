/**
 * 业务配置
 * 包含项目特定的业务逻辑配置
 */

// 展示字段配置（从 exhibitionFieldConfig.ts 迁移）
export const exhibitionFieldConfig = [
  {
    key: 'people',
    label: '人口',
    fields: ['type', 'idCard'] // 证件类型、证件号码
  },
  {
    key: 'place',
    label: '场所',
    fields: ['uscc'] // 统一信用代码
  },
  {
    key: 'property',
    label: '物地',
    fields: ['code'] // 编码
  },
  {
    key: 'standard',
    label: '一标三实',
    fields: ['standardCode'] // 标准编码
  }
] as const

// 导出类型
export type ExhibitionFieldConfig = typeof exhibitionFieldConfig[number]
