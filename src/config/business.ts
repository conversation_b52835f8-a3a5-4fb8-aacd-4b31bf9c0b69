/**
 * 业务配置
 * 包含项目特定的业务逻辑配置
 */

// 展示字段配置（从 exhibitionFieldConfig.ts 迁移）
export const exhibitionFieldConfig = [
  {
    key: 'people',
    label: '人口',
    fields: ['type', 'idCard'] // 证件类型、证件号码
  },
  {
    key: 'place',
    label: '场所',
    fields: ['uscc'] // 统一信用代码
  },
  {
    key: 'property',
    label: '物地',
    fields: ['code'] // 编码
  },
  {
    key: 'standard',
    label: '一标三实',
    fields: ['standardCode'] // 标准编码
  }
] as const

// 管理ID配置
export const manageIdConfig = {
  // 默认管理ID
  default: '1942420981721182210',
  
  // 标签管理ID
  label: '1932725509586165761',
  
  // 获取管理ID的方法
  getManageId: (type?: string): string => {
    switch (type) {
      case 'label':
        return manageIdConfig.label
      default:
        return manageIdConfig.default
    }
  }
} as const

// 字段类型映射配置
export const fieldTypeMapping = {
  // 字段类型到组件的映射
  componentMap: {
    1: 'ElInput',        // 文本
    2: 'ElInputNumber',  // 数字
    3: 'ElRadioGroup',   // 单选
    4: 'ElCheckboxGroup', // 多选
    5: 'ElDatePicker',   // 日期
    6: 'ElDatePicker',   // 日期区间
    7: 'AddressSelect',  // 地址选择
    8: 'RegionSelect',   // 区域
    9: 'TagSelect',      // 标签
    10: 'FileUpload'     // 附件
  },
  
  // 字段类型到验证规则的映射
  validationMap: {
    1: 'text',
    2: 'number',
    3: 'radio',
    4: 'checkbox',
    5: 'date',
    6: 'dateRange',
    7: 'address',
    8: 'region',
    9: 'tag',
    10: 'file'
  }
} as const

// 表单布局配置
export const formLayoutConfig = {
  // 默认列数
  defaultCols: 2,
  
  // 响应式断点配置
  breakpoints: {
    xs: 1,  // <768px
    sm: 1,  // ≥768px
    md: 2,  // ≥992px
    lg: 2,  // ≥1200px
    xl: 3,  // ≥1920px
  },
  
  // 字段跨度配置
  spanConfig: {
    // 单行文本
    text: 12,
    // 多行文本
    textarea: 24,
    // 日期区间
    dateRange: 24,
    // 地址选择
    address: 24,
    // 附件上传
    file: 24
  }
} as const

// 数据验证配置
export const validationConfig = {
  // 正则表达式映射
  patterns: {
    // 身份证号
    idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    
    // 手机号
    mobile: /^1[3-9]\d{9}$/,
    
    // 电话号码
    phone: /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/,
    
    // 统一社会信用代码
    uscc: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
    
    // 邮箱
    email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    
    // 网址
    url: /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i
  },
  
  // 错误消息模板
  messages: {
    required: '请输入{field}',
    pattern: '{field}格式不正确',
    min: '{field}不能少于{min}个字符',
    max: '{field}不能超过{max}个字符',
    range: '{field}长度应在{min}-{max}个字符之间'
  }
} as const

// 操作配置
export const operationConfig = {
  // 操作类型映射
  typeMap: {
    create: '新增',
    edit: '编辑',
    delete: '删除',
    view: '查看',
    export: '导出',
    import: '导入'
  },
  
  // 操作按钮配置
  buttonConfig: {
    create: { type: 'primary', icon: 'Plus' },
    edit: { type: 'primary', icon: 'Edit' },
    delete: { type: 'danger', icon: 'Delete' },
    view: { type: 'info', icon: 'View' },
    export: { type: 'success', icon: 'Download' },
    import: { type: 'warning', icon: 'Upload' }
  }
} as const

// 状态配置
export const statusConfig = {
  // 通用状态
  common: {
    enabled: { value: 1, label: '启用', color: 'success' },
    disabled: { value: 0, label: '禁用', color: 'danger' }
  },
  
  // 审核状态
  audit: {
    pending: { value: 0, label: '待审核', color: 'warning' },
    approved: { value: 1, label: '已通过', color: 'success' },
    rejected: { value: 2, label: '已拒绝', color: 'danger' }
  },
  
  // 数据状态
  data: {
    normal: { value: 0, label: '正常', color: 'success' },
    deleted: { value: 1, label: '已删除', color: 'danger' },
    archived: { value: 2, label: '已归档', color: 'info' }
  }
} as const

// 权限配置
export const permissionConfig = {
  // 权限级别
  levels: {
    admin: 'admin',
    manager: 'manager',
    user: 'user',
    guest: 'guest'
  },
  
  // 权限操作
  actions: {
    create: 'create',
    read: 'read',
    update: 'update',
    delete: 'delete',
    export: 'export',
    import: 'import'
  }
} as const

// 导出类型
export type ExhibitionFieldConfig = typeof exhibitionFieldConfig[number]
export type FieldTypeComponent = keyof typeof fieldTypeMapping.componentMap
export type ValidationPattern = keyof typeof validationConfig.patterns
export type OperationType = keyof typeof operationConfig.typeMap
export type StatusType = keyof typeof statusConfig.common
export type PermissionLevel = keyof typeof permissionConfig.levels
export type PermissionAction = keyof typeof permissionConfig.actions
