import { FormSchema } from './types'

export const iotFormShema: FormSchema[] = [
  { label: 'iotId（设备编码）', prop: 'iotId', type: 'input' },
  { label: '备注信息', prop: 'description', type: 'input' },
  { label: '设备密钥', prop: 'deviceSecret', type: 'input' },
  // { label: '设备的标签', prop: 'deviceTagList', type: 'input' },
  { label: '租户ID', prop: 'orgId', type: 'input' },
  { label: '设备标识符', prop: 'deviceKey', type: 'input' },
  // { label: '设备名称', prop: 'deviceName', type: 'input' },
  { label: '设备创建者', prop: 'createdBy', type: 'input' },
  // { label: '创建时间', prop: 'createdOn', type: 'date', timestamp: true },//清除和公共部分重复字段
  { label: '最近更新者', prop: 'lastUpdatedBy', type: 'input' },
  { label: '最近更新时间', prop: 'lastUpdatedOn', type: 'date', timestamp: true },
  { label: '激活时间', prop: 'activatedOn', type: 'date', timestamp: true },
  { label: '最后上线时间', prop: 'lastOnlineOn', type: 'date', timestamp: true },
  { label: 'IP地址', prop: 'ip', type: 'input' },
  {
    label: '节点类型',
    prop: 'nodeType',
    type: 'select',
    options: [
      { label: '设备', value: '0' },
      { label: '网关', value: '1' },
      { label: '子设备', value: '2' }
    ]
  },
  {
    label: '认证方式',
    prop: 'authType',
    type: 'select',
    options: [
      { label: '设备密钥', value: '0' },
      { label: 'X.509证书', value: '1' }
    ]
  },
  {
    label: '设备接入协议',
    prop: 'deviceConnectionProtocol',
    type: 'select',
    options: [
      { label: 'MQTT', value: '0' },
      { label: 'CoAP/LWM2M', value: '1' },
      { label: 'HTTP', value: '2' },
      { label: 'TCP', value: '3' },
      { label: 'CoAP', value: '4' },
      { label: 'JT/T 808', value: '5' }
    ]
  },
  { label: '所属产品key', prop: 'productKey', type: 'input' },
  { label: '所属产品名称', prop: 'productName', type: 'input' },
  {
    label: '设备状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '在线', value: '0' },
      { label: '离线', value: '1' },
      { label: '未激活', value: '2' }
    ]
  },
  {
    label: '是否启用',
    prop: 'enabled',
    type: 'select',
    options: [
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ]
  },
  { label: '设备IMEI', prop: 'imei', type: 'input' },
  { label: '位置', prop: 'position', type: 'input' },
  // {
  //   label: '经纬度',
  //   prop: ['longitude', 'latitude'],
  //   type: 'location'
  // },
  { label: 'PSK密钥', prop: 'psk', type: 'input' },
  { label: '生命周期', prop: 'lifetime', type: 'input' }, //生命周期字段暂时当数字处理
  {
    label: '订阅模式',
    prop: 'observeMode',
    type: 'select',
    options: [
      { label: '自动订阅', value: '0' },
      { label: '手动订阅', value: '1' }
    ]
  },
  { label: '设备授权码', prop: 'authCode', type: 'input' },
  { label: '终端注册实体', prop: 'deviceTerminalRegistrationEntity', type: 'input' }
]

export const iotData = Object.fromEntries(
  iotFormShema.flatMap((item) =>
    Array.isArray(item.prop)
      ? item.prop.map((p) => [p, undefined])
      : [[item.prop, item.multiple ? [] : undefined]]
  )
)
