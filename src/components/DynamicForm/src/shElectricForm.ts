import { FormSchema } from './types'

export const shElectricFormShema: FormSchema[] = [
  // { label: '设备编号', prop: 'devNo', type: 'input' },//应产品需求重复不展示
  // { label: '设备名称', prop: 'devName', type: 'input' },//应产品需求重复不展示
  // { label: '设备类型', prop: 'devType', type: 'input' },
  // { label: 'IP地址', prop: 'ip', type: 'input' },//应产品需求重复不展示
  { label: '购置日期', prop: 'purchaseDate', type: 'date', timestamp: true },
  { label: '品牌', prop: 'brand', type: 'input' },
  // {
  //   label: '经纬度',
  //   prop: ['longitude', 'latitude'],
  //   type: 'location'
  // },
  {
    label: '设备状态',
    prop: 'devState',
    type: 'select',
    options: [
      { label: '待用', value: 0 },
      { label: '待复核', value: 1 },
      { label: '未通过', value: 2 },
      { label: '复核通过', value: 3 },
      { label: '启用', value: 4 },
      { label: '停用', value: 5 },
      { label: '报废', value: 6 },
      { label: '报修', value: 7 },
      { label: '修复', value: 8 }
    ]
  },
  // { label: '修改人', prop: 'updateUser', type: 'input' },
  // { label: '创建人', prop: 'createUser', type: 'input' },
  // { label: '创建时间', prop: 'createTime', type: 'date', timestamp: true },
  // { label: '修改时间', prop: 'updateTime', type: 'input' },
  { label: '批次号', prop: 'batch', type: 'input' },
  { label: '型号', prop: 'devModel', type: 'input' },
  { label: '资产原值', prop: 'originalPay', type: 'input' },
  { label: '净残值率', prop: 'badRate', type: 'input' },
  // { label: 'url1', prop: 'url1', type: 'input' },//备用字段暂时隐藏
  // { label: 'url2', prop: 'url2', type: 'input' },
  // { label: 'url3', prop: 'url3', type: 'input' },
  { label: '质保年限', prop: 'useYear', type: 'input' },
  { label: '质保开始年限', prop: 'startTime', type: 'input' },
  { label: '质保结束年限', prop: 'endTime', type: 'input' },
  { label: '折旧年限', prop: 'depreciationTime', type: 'input' },
  { label: '负责人', prop: 'chargeUser', type: 'input' },
  { label: '联系方式', prop: 'contact', type: 'input' },
  { label: '描述', prop: 'description', type: 'input' },
  { label: '折旧开始时间', prop: 'depreciationStart', type: 'date', timestamp: true },
  { label: '端口', prop: 'port', type: 'input' },
  { label: '单位ID', prop: 'unitId', type: 'input' },
  { label: '项目ID', prop: 'projectId', type: 'input' },
  { label: '删除标识', prop: 'isDelete', type: 'input' },
  // { label: '备字段1', prop: 'bak1', type: 'input' },//备用字段暂时隐藏
  // { label: '备字段2', prop: 'bak2', type: 'input' },
  // { label: '备字段3', prop: 'bak3', type: 'input' },
  // { label: '备字段4', prop: 'bak4', type: 'input' },
  // { label: '备字段5', prop: 'bak5', type: 'input' },
  // { label: '备字段6', prop: 'bak6', type: 'input' },
  // { label: '备字段7', prop: 'bak7', type: 'input' },
  // { label: '备字段8', prop: 'bak8', type: 'input' },
  // { label: '备字段9', prop: 'bak9', type: 'input' },
  // { label: '备字段10', prop: 'bak10', type: 'input' },
  { label: '养护类型', prop: 'repairType', type: 'input' }
]

export const shElectricData = Object.fromEntries(
  shElectricFormShema.flatMap((item) =>
    Array.isArray(item.prop)
      ? item.prop.map((p) => [p, undefined])
      : [[item.prop, item.multiple ? [] : undefined]]
  )
)
