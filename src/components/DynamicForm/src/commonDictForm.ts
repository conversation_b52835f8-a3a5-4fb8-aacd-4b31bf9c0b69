import { FormSchema } from './types'
import { getDictList } from '@/api/infra/deviceDict'
import * as DynamicFormApi from '@/api/DynamicForm'
import {getUnitMax} from "@/api/DynamicForm";
const getOperatingUnit = async (type: string) => {
  const data = await DynamicFormApi.getUnitType({ code: type })
  return () => DynamicFormApi.getUnitMax({ typeId: data })
}

export const commonFormShema: FormSchema[] = [
  {
    label: '设备来源',
    prop: 'assetsSource',
    type: 'select',
    api: () => getDictList('deviceSource'),
    required: true,
    labelKey: 'value'
  },
  { label: '设备编号', prop: 'assetsCode', type: 'input', required: true },
  { label: '设备名称', prop: 'assetsName', type: 'input', required: true },
  {
    label: '所属项目',
    prop: 'projectId',
    type: 'select',
    api: () => DynamicFormApi.getBelongsProject(),
    labelKey: 'projectName',
    // required: true
  },
  {
    label: '所属单位',
    prop: 'deptId',
    type: 'select',
    api: () => DynamicFormApi.getBelongsUnit(),
    labelKey: 'name'
  },
  {
    label: '设备类型',
    prop: 'assetsTypeId',
    type: 'tree',
    api: () => DynamicFormApi.getType(),
    labelKey: 'name',
    required: true,
  },

  {
    label: '所属站点',
    prop: 'siteId',
    type: 'select',
    api: async () => {
      const { list } = await DynamicFormApi.getSite({ pageSize: -1 })
      return list
    },
    // options: [
    //   { label: '标签1', id: 1 },
    //   { label: '标签2', id: 2 }
    // ],
    labelKey: 'siteName'
  },
  {
    label: '所属区域',
    prop: 'areaId',
    type: 'tree',
    api: () => DynamicFormApi.getSuppliers({ dictType: 'oamArea' }),
    labelKey: 'name'
  },
  { label: '详细位置', prop: 'address', type: 'input' },
  { label: 'IP地址', prop: 'ip', type: 'input' },
  {
    label: '供应商',
    prop: 'supplierId',
    type: 'select',
    api: () => DynamicFormApi.getSuppliers({ dictType: 'supplierDirectory' }),
    labelKey: 'name'
  },
  {
    label: '规格型号',
    prop: 'modelId',
    type: 'select',
    api: () => DynamicFormApi.getSuppliers({ dictType: 'modelConfig' }),
    labelKey: 'name'
  },
  { label: '质保日期', prop: 'warrantyDate', type: 'date' },
  {
    label: '设备标签',
    prop: 'labelIds',
    type: 'select',
    api: () => DynamicFormApi.getSuppliers({ dictType: 'deviceLabel' }),
    labelKey: 'name',
    multiple: true
    // options: [
    //   { label: '标签1', id: 1 },
    //   { label: '标签2', id: 2 }
    // ]
  },
  // {
  //   label: '是否重点',
  //   prop: 'isKey',
  //   type: 'select',
  //   options: [
  //     { label: '否', id: 0 },
  //     { label: '是', id: 1 }
  //   ]
  // },
  {
    label: '保障分组',
    prop: 'groupId',
    type: 'select',
    api: () => DynamicFormApi.getPacket(),
    labelKey: 'groupName'
  },
  {
    label: '经纬度',
    prop: ['longitude', 'latitude'],
    type: 'location'
  },
  { label: '二维码地址', prop: 'qrCode', type: 'input' },
  {
    label: '告警方式',
    prop: 'warnId',
    type: 'select',
    api: () => DynamicFormApi.getWarning(),
    labelKey: 'warnTypeName'
  },
  {
    label: '运营商',
    prop: 'operatingUnit',
    type: 'select',
    api: await getOperatingUnit('operation'),
    labelKey: 'name'
  },
  {
    label: '维保单位',
    prop: 'maintenanceUnit',
    type: 'select',
    api: await getOperatingUnit('maintenance'),
    labelKey: 'name'
  }
]

export const commonFormData = Object.fromEntries(
  commonFormShema.flatMap((item) =>
    Array.isArray(item.prop)
      ? item.prop.map((p) => [p, undefined])
      : [[item.prop, item.multiple ? [] : undefined]]
  )
)
