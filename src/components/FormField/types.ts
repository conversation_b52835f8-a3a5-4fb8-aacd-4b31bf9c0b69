/**
 * 表单字段组件共享类型定义
 * 整合项目现有类型，避免重复定义
 */

import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'

// 重新导出项目中已有的类型
export type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'

// 表单值类型（兼容项目现有类型）
export type FormValueType = string | number | boolean | string[] | number[] | Date | Date[] | null | undefined

// 选项接口（与项目现有结构保持一致）
export interface Option {
  label: string
  value: string | number
  disabled?: boolean
  children?: Option[]
}

// 字段扩展配置接口（与现有 API 结构保持一致）
export interface FieldExtConfig {
  name: string
  value: any
  optionsJson?: Option[]
}

// 统一字段配置接口（支持完整配置和简化配置）
export type FieldConfig = LabelFieldConfig | SimpleFieldConfig

// 简化的字段配置接口（向后兼容）
export interface SimpleFieldConfig {
  code: string
  name: string
  fieldType: number
  remark?: string
  length?: number
  fieldConfExtDOList?: FieldExtConfig[]
}

// 字段组件基础属性接口
export interface BaseFieldProps {
  field: FieldConfig
  modelValue: FormValueType
  fieldOptions?: Option[]
}

// 字段组件事件接口
export interface FieldEmits {
  (e: 'update:modelValue', value: FormValueType): void
}

// 文本字段特有配置
export interface TextFieldConfig {
  textType?: '0' | '1' // 0: 单行文本, 1: 多行文本
  maxLength?: number
  minLength?: number
  placeholder?: string
  readonly?: boolean
  disabled?: boolean
}

// 数字字段特有配置
export interface NumberFieldConfig {
  numberType?: '0' | '1' // 0: 整数, 1: 小数
  decimalPlaces?: string // 小数位数
  minValue?: number
  maxValue?: number
  step?: number
  placeholder?: string
}

// 选择字段特有配置
export interface SelectFieldConfig {
  multiple?: boolean
  clearable?: boolean
  filterable?: boolean
  placeholder?: string
  options?: Option[]
}

// 日期字段特有配置
export interface DateFieldConfig {
  datePrecision?: 'year' | 'month' | 'date' | 'hour' | 'minute' | 'second'
  minDate?: string
  maxDate?: string
  clearable?: boolean
  placeholder?: string
}

// 文件字段特有配置
export interface FileFieldConfig {
  allowedTypes?: string[] // 允许的文件类型
  countLimit?: number // 文件数量限制
  sizeLimit?: number // 文件大小限制(MB)
  multiple?: boolean // 是否支持多文件
}

// 标签字段特有配置
export interface TagFieldConfig {
  maxTags?: number // 最大标签数量
  maxTagLength?: number // 单个标签最大长度
  tagType?: 'primary' | 'success' | 'info' | 'warning' | 'danger'
  tagSize?: 'large' | 'default' | 'small'
  showTagCount?: boolean // 是否显示标签数量
  readonly?: boolean
}

// 地区字段特有配置
export interface RegionFieldConfig {
  multiple?: boolean
  clearable?: boolean
  filterable?: boolean
  checkStrictly?: boolean // 是否严格模式
  nodeKey?: string // 节点唯一标识
  labelKey?: string // 显示字段
  valueKey?: string // 值字段
  childrenKey?: string // 子节点字段
  treeData?: any[] // 树形数据
}

// 字段验证规则接口
export interface FieldValidationRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change'
  validator?: (rule: any, value: any, callback: any) => void
}

// 字段组件注册映射类型
export type FieldComponentMap = {
  [key: number]: any
}

// 工具函数类型
export type FieldConfigExtractor<T> = (field: SimpleFieldConfig) => T

// 字段值类型
export type FieldValue = string | number | boolean | string[] | number[] | Date | Date[] | null | undefined
