<template>
  <el-form-item :label="field.name" :prop="field.code" :required="isRequired">
    <!-- 文本输入框 -->
    <el-input
      v-if="field.fieldType === FieldType.TEXT"
      v-model.trim="modelValue"
      :placeholder="getPlaceholder(field)"
      :maxlength="getFieldLength(field)"
      :type="getTextType(field)"
      :rows="getTextRows(field)"
      @update:modelValue="handleUpdate"
    />

    <!-- 数字输入框 -->
    <el-input-number
      v-else-if="field.fieldType === FieldType.NUMBER"
      v-model="modelValue"
      @update:modelValue="handleUpdate"
    />

    <!-- 下拉框/单选框/多选框 -->
    <el-select
      v-else-if="field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX"
      v-model="modelValue"
      :multiple="field.fieldType === FieldType.CHECKBOX"
      :placeholder="`请选择${field.name}`"
      @update:modelValue="handleUpdate"
    >
      <el-option
        v-for="option in getFieldOptions(field)"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>

    <!-- 单个时间选择器 -->
    <el-date-picker
      v-else-if="field.fieldType === FieldType.DATE"
      v-model="modelValue"
      :type="getPickerType(field)"
      :format="getPickerFormat(field)"
      value-format="YYYY/MM/DD HH:mm:ss"
      :placeholder="`请选择${field.name}`"
      class="w-full"
      @update:modelValue="handleUpdate"
    />

    <!-- 时间范围选择器 -->
    <el-date-picker
      v-else-if="field.fieldType === FieldType.DATE_RANGE"
      v-model="modelValue"
      type="datetimerange"
      range-separator="至"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :format="getPickerFormat(field)"
      value-format="YYYY/MM/DD HH:mm:ss"
      :placeholder="`请选择${field.name}`"
      @update:modelValue="handleUpdate"
    />

    <!-- 文件上传 -->
    <UploadFile
      v-else-if="field.fieldType === FieldType.ATTACHMENT"
      v-model="modelValue"
      :file-type="getFileTypes(field)"
      :limit="getFileLimit(field)"
      :file-size="getFileSize(field)"
      class="min-w-80px"
      @update:modelValue="handleUpdate"
    />

    <!-- 标签输入 -->
    <el-input
      v-else-if="field.fieldType === FieldType.TAG"
      v-model="modelValue"
      @update:modelValue="handleUpdate"
    />

    <!-- 地区选择 -->
    <el-tree-select
      v-else-if="field.fieldType === FieldType.REGION"
      :data="fieldOptions as any[]"
      v-model="modelValue"
      :render-after-expand="false"
      @update:modelValue="handleUpdate"
    />

    <!-- 默认文本输入 -->
    <el-input
      v-else
      v-model="modelValue"
      :placeholder="`请输入${field.name}`"
      @update:modelValue="handleUpdate"
    />
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { UploadFile } from '@/components/UploadFile'
import { FieldType } from '@/config/constants/enums/field'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'

// 重新导出 FieldType 枚举
export { FieldType }

interface Option {
  label: string
  value: string | number
}

// 简化的字段配置接口
interface SimpleFieldConfig {
  code: string
  name: string
  fieldType: number
  remark?: string
  length?: number
  fieldConfExtDOList?: Array<{ name: string; value: any; optionsJson?: any[] }>
}

interface Props {
  field: LabelFieldConfig | SimpleFieldConfig
  modelValue: any
  fieldOptions?: Option[]
}

const props = withDefaults(defineProps<Props>(), {
  fieldOptions: () => []
})

const emit = defineEmits(['update:modelValue'])

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}

// 计算是否必填
const isRequired = computed(() => {
  // 根据业务逻辑判断是否必填，这里可以根据实际需求调整
  return false
})

// 获取占位符
const getPlaceholder = (field: LabelFieldConfig | SimpleFieldConfig): string => {
  return field.remark || `请输入${field.name}`
}

// 获取字段长度
const getFieldLength = (field: LabelFieldConfig | SimpleFieldConfig): number | undefined => {
  return 'length' in field ? field.length : undefined
}

// 获取文本输入框类型
const getTextType = (field: LabelFieldConfig | SimpleFieldConfig): string => {
  const textTypeConfig = field.fieldConfExtDOList?.find(item => item.name === 'textType')
  return textTypeConfig?.value === '1' ? 'textarea' : 'text'
}

// 获取文本输入框行数
const getTextRows = (field: LabelFieldConfig | SimpleFieldConfig): number => {
  const textTypeConfig = field.fieldConfExtDOList?.find(item => item.name === 'textType')
  return textTypeConfig?.value === '1' ? 2 : 1
}

// 获取时间选择器类型
const getPickerType = (field: LabelFieldConfig | SimpleFieldConfig): 'date' | 'datetime' | 'datetimerange' | 'daterange' => {
  const datePrecisionConfig = field.fieldConfExtDOList?.find(item => item.name === 'datePrecision')
  const precision = String(datePrecisionConfig?.value || 'date')

  switch (precision) {
    case 'year':
      return 'date'
    case 'month':
      return 'date'
    case 'date':
      return 'date'
    case 'hour':
    case 'minute':
    case 'second':
      return 'datetime'
    default:
      return 'datetime'
  }
}

// 获取时间选择器格式
const getPickerFormat = (field: LabelFieldConfig | SimpleFieldConfig): string => {
  const datePrecisionConfig = field.fieldConfExtDOList?.find(item => item.name === 'datePrecision')
  const precision = String(datePrecisionConfig?.value || 'date')

  switch (precision) {
    case 'year':
      return 'YYYY'
    case 'month':
      return 'YYYY/MM'
    case 'date':
      return 'YYYY/MM/DD'
    case 'hour':
      return 'YYYY/MM/DD HH:00'
    case 'minute':
      return 'YYYY/MM/DD HH:mm'
    case 'second':
      return 'YYYY/MM/DD HH:mm:ss'
    default:
      return 'YYYY/MM/DD HH:mm:ss'
  }
}

// 获取文件类型数组
const getFileTypes = (field: LabelFieldConfig | SimpleFieldConfig): string[] => {
  const allowedTypesConfig = field.fieldConfExtDOList?.find(item => item.name === 'allowedTypes')
  if (!allowedTypesConfig?.value) return ['doc', 'xls', 'ppt', 'txt', 'pdf']
  return String(allowedTypesConfig.value).split(',').map(type => type.trim())
}

// 获取文件数量限制
const getFileLimit = (field: LabelFieldConfig | SimpleFieldConfig): number => {
  const countLimitConfig = field.fieldConfExtDOList?.find(item => item.name === 'countLimit')
  return countLimitConfig?.value || 5
}

// 获取文件大小限制
const getFileSize = (field: LabelFieldConfig | SimpleFieldConfig): number => {
  const sizeLimitConfig = field.fieldConfExtDOList?.find(item => item.name === 'sizeLimit')
  return sizeLimitConfig?.value || 5
}

// 获取字段选项
const getFieldOptions = (field: LabelFieldConfig | SimpleFieldConfig): Option[] => {
  const optionsConfig = field.fieldConfExtDOList?.find(item => item.name === 'optionsJson')
  if (optionsConfig?.optionsJson) {
    return optionsConfig.optionsJson
  }
  return props.fieldOptions
}
</script>