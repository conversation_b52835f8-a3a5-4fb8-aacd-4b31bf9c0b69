<template>
  <el-input
    :model-value="modelValue as string"
    :placeholder="placeholder"
    :maxlength="field.length"
    :type="textType"
    :rows="textRows"
    :disabled="styleConfig.disabled"
    :readonly="styleConfig.readonly"
    :show-word-limit="showWordLimit"
    :clearable="!styleConfig.readonly"
    @update:model-value="handleUpdate"
    class="w-full"
  />
</template>

<script setup lang="ts">
import type { BaseFieldProps, FieldEmits } from '../types'
import { useFieldConfig } from '@/hooks/web/useFieldConfig'

interface Props extends BaseFieldProps {}

const props = defineProps<Props>()
const emit = defineEmits<FieldEmits>()

const { getPlaceholder, getBooleanConfig, getNumberConfig, styleConfig } = useFieldConfig(computed(() => props.field))

// 处理值更新，自动去除字符串首尾空格
const handleUpdate = (value: any) => {
  const trimmedValue = typeof value === 'string' ? value.trim() : value
  emit('update:modelValue', trimmedValue)
}

// 计算占位符
const placeholder = computed(() => getPlaceholder())

// 计算文本输入框类型
const textType = computed(() => {
  return getBooleanConfig('isMultiline') ? 'textarea' : 'text'
})

// 计算文本输入框行数
const textRows = computed(() => {
  return getNumberConfig('rows', 2)
})

// 是否显示字数统计
const showWordLimit = computed(() => {
  return getBooleanConfig('showWordLimit') && !!props.field.length
})
</script>
