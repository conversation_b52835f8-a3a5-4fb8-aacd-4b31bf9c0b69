<template>
  <el-input-number
    :model-value="modelValue"
    :placeholder="placeholder"
    :precision="precision"
    :min="minValue"
    :max="maxValue"
    :step="step"
    class="w-full"
    @update:model-value="handleUpdate"
  />
</template>

<script setup lang="ts">
import type { SimpleFieldConfig } from '../types'

interface Props {
  field: SimpleFieldConfig
  modelValue: any
  fieldOptions?: any[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}

// 计算占位符
const placeholder = computed(() => {
  return props.field.remark || `请输入${props.field.name}`
})

// 计算数字精度
const precision = computed(() => {
  const numberTypeConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'numberType')
  const decimalPlacesConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'decimalPlaces')

  if (numberTypeConfig?.value === '0') {
    return 0 // 整数
  }

  return parseInt(decimalPlacesConfig?.value || '2', 10)
})

// 计算最小值
const minValue = computed(() => {
  const minConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'minValue')
  return minConfig?.value ? Number(minConfig.value) : undefined
})

// 计算最大值
const maxValue = computed(() => {
  const maxConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'maxValue')
  return maxConfig?.value ? Number(maxConfig.value) : undefined
})

// 计算步长
const step = computed(() => {
  const stepConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'step')
  return stepConfig?.value ? Number(stepConfig.value) : 1
})
</script>
