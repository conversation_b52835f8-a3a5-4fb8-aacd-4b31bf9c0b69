<template>
  <el-select
    :model-value="modelValue"
    :multiple="isMultiple"
    :placeholder="placeholder"
    :clearable="clearable"
    :filterable="filterable"
    class="w-full"
    @update:model-value="handleUpdate"
  >
    <el-option
      v-for="option in options"
      :key="option.value"
      :label="option.label"
      :value="option.value"
      :disabled="option.disabled"
    />
  </el-select>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { FieldType } from '@/config/constants/enums/field'
import type { SimpleFieldConfig, Option } from '../types'

interface Props {
  field: SimpleFieldConfig
  modelValue: any
  fieldOptions?: Option[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}

// 计算占位符
const placeholder = computed(() => {
  return `请选择${props.field.name}`
})

// 是否多选
const isMultiple = computed(() => {
  return props.field.fieldType === FieldType.CHECKBOX
})

// 是否可清空
const clearable = computed(() => {
  const clearableConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'clearable')
  return clearableConfig?.value !== '0' // 默认可清空
})

// 是否可搜索
const filterable = computed(() => {
  const filterableConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'filterable')
  return filterableConfig?.value === '1'
})

// 计算选项列表
const options = computed((): Option[] => {
  //todo 这里可能调词典接口，获取下拉框的选项
  return props.fieldOptions || []
})
</script>
