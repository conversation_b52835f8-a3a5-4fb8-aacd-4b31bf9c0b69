import { ref, reactive } from 'vue'
import { FormRules } from 'element-plus'
import * as FieldConfApi from '@/api/system/data/field-conf'
import * as ViewFormConfApi from '@/api/system/data/view-form-conf'
import * as BusinessDataApi from '@/api/system/business-data'
import { filterAndMarkGroups } from '@/utils/formatter'
import { FormConfigProcessor } from '@/utils/formDataProcessor'
import { ViewFormType } from '@/config/constants/enums/label'

/**
 * 表单配置管理 Hook
 */
export function useFormConfig() {
  const fieldGroups = ref<any[]>([])
  const formRules = reactive<FormRules>({})

  /**
   * 获取字段配置
   */
  const getFieldConfigs = async (manageId: string) => {
    return await FieldConfApi.getFieldConfigListByManageId({ manageId })
  }

  /**
   * 获取业务数据
   */
  const getBusinessData = async (id: string, manageId: string) => {
    return await BusinessDataApi.getBusinessData({ id, manageId })
  }

  /**
   * 获取表单配置数据
   */
  const getFormConfigData = async (
    manageId: string, 
    formType: number, 
    flagKey: string, 
    fieldConfigs: any[]
  ) => {
    const filteredRes = FormConfigProcessor.getFilteredFieldConfigs(fieldConfigs, flagKey)
    const formConfData = await ViewFormConfApi.getViewFormConf({
      manageId,
      formType
    })
    
    const rawData = FormConfigProcessor.parseFormConfig(formConfData)
    const allowedIds = FormConfigProcessor.getAllowedIds(filteredRes)
    const filteredData = filterAndMarkGroups(rawData, allowedIds)
    
    return filteredData
  }

  /**
   * 获取基础配置信息
   */
  const getBaseConfig = (route: any, editType: any) => {
    const manageId = '1942420981721182210' // 可以从配置或路由中获取
    const { type = 'create', id } = route.query
    
    editType.value = type as ('create' | 'edit')
    const formType = type === 'create' ? ViewFormType.CREATE : ViewFormType.EDIT
    const flagKey = type === 'create' ? 'addFlag' : 'editFlag'
    
    return { manageId, type: type as string, id: id as string | null, formType, flagKey }
  }

  /**
   * 获取配置和业务数据
   */
  const fetchConfigAndBusinessData = async (manageId: string, type: string, id: string | null) => {
    const fieldConfigs = await getFieldConfigs(manageId)
    
    let businessData = {}
    if (type === 'edit' && id) {
      businessData = await getBusinessData(id, manageId)
    }
    
    return { fieldConfigs, businessData }
  }

  return {
    fieldGroups,
    formRules,
    getFieldConfigs,
    getBusinessData,
    getFormConfigData,
    getBaseConfig,
    fetchConfigAndBusinessData
  }
}
