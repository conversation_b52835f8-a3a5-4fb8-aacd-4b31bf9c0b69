import { ref, reactive } from 'vue'
import { FormRules } from 'element-plus'
import * as FieldConfApi from '@/api/system/data/field-conf'
import * as ViewFormConfApi from '@/api/system/data/view-form-conf'
import * as BusinessDataApi from '@/api/system/business-data'
import * as DictData<PERSON>pi from '@/api/system/dict/dict.data'
import { filterAndMarkGroups } from '@/utils/formatter'
import { FormConfigProcessor, FormDataProcessor } from '@/utils/formDataProcessor'
import { ViewFormType } from '@/config/constants/enums/label'
import { FieldType } from '@/config/constants/enums/field'
import {
  validatePatternMapNumber,
  validateUSCC,
  createRequiredRule,
  createRegexRule
} from '@/utils/formRules'
import type { FieldGroup } from '@/config/constants/types'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'
import type { BusinessData } from '@/api/system/business-data'

/**
 * 表单配置管理 Hook
 */
export function useFormConfig() {
  const fieldGroups = ref<any[]>([])
  const formData = ref<Record<string, any>>({})
  const formRules = reactive<FormRules>({})
  const fieldOptionsMap = ref(new Map<string, any[]>())

  /**
   * 获取字段配置
   */
  const getFieldConfigs = async (manageId: string): Promise<LabelFieldConfig[]> => {
    return await FieldConfApi.getFieldConfigListByManageId({ manageId })
  }

  /**
   * 获取业务数据
   */
  const getBusinessData = async (id: string, manageId: string, sensitivePwd?: string): Promise<BusinessData> => {
    return await BusinessDataApi.getBusinessData({ id, manageId, sensitivePwd })
  }

  /**
   * 获取字段选项
   */
  const fetchFieldOptions = async (field: LabelFieldConfig): Promise<any[]> => {
    const { list } = await DictDataApi.getDictDataPage({
      pageNo: 1,
      pageSize: 10,
      dictType: field.fieldConfExtDOList[0].value
    })
    fieldOptionsMap.value.set(field.code, list)
    return list
  }

  /**
   * 获取字段选项（同步方法）
   */
  const getFieldOptions = (field: LabelFieldConfig): any[] => {
    if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
      return fieldOptionsMap.value.get(field.code) || []
    }
    return []
  }

  /**
   * 获取表单配置数据
   */
  const getFormConfigData = async (
    manageId: string,
    formType: number,
    flagKey: string,
    fieldConfigs: LabelFieldConfig[]
  ): Promise<FieldGroup[]> => {
    const filteredRes = FormConfigProcessor.getFilteredFieldConfigs(fieldConfigs, flagKey)
    const formConfData = await ViewFormConfApi.getViewFormConf({
      manageId,
      formType
    })

    const rawData = FormConfigProcessor.parseFormConfig(formConfData)
    const allowedIds = FormConfigProcessor.getAllowedIds(filteredRes)
    const filteredData = filterAndMarkGroups(rawData, allowedIds)

    return filteredData
  }

  /**
   * 生成表单验证规则
   */
  const generateFormRules = (fields: any[]): FormRules => {
    return fields.reduce((rules: FormRules, field) => {
      const ruleArr: any[] = []

      // 必填验证
      if (field.required) ruleArr.push(createRequiredRule(field.name))

      // 自定义正则验证
      if (field.fieldType === 1) {
        const { required, fieldConfExtObj } = field
        const { regex, prompt = '格式不正确', dataValidation } = fieldConfExtObj
        if (dataValidation === '1' && regex) {
          ruleArr.push(createRegexRule(regex, prompt, required))
        } else if (dataValidation === '3') {
          ruleArr.push({
            validator: (_rule: any, value: any, callback: any) => {
              if (!required && !value) return callback()
              if (!validateUSCC(value)) {
                callback(new Error(prompt))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          })
        } else {
          const regex = validatePatternMapNumber[dataValidation]
          ruleArr.push(createRegexRule(regex, prompt, required))
        }
      }
      if (ruleArr.length) rules[field.code] = ruleArr
      return rules
    }, {})
  }

  /**
   * 获取基础配置信息
   */
  const getBaseConfig = (route: any, editType?: any) => {
    const manageId = '1942420981721182210' // 可以从配置或路由中获取
    const { type = 'create', id } = route.query

    if (editType) {
      editType.value = type as ('create' | 'edit')
    }
    const formType = type === 'create' ? ViewFormType.CREATE : ViewFormType.EDIT
    const flagKey = type === 'create' ? 'addFlag' : 'editFlag'

    return { manageId, type: type as string, id: id as string | null, formType, flagKey }
  }

  /**
   * 获取配置和业务数据
   */
  const fetchConfigAndBusinessData = async (
    manageId: string,
    type: string,
    id: string | null,
    sensitivePwd?: string
  ): Promise<{ fieldConfigs: LabelFieldConfig[], businessData: BusinessData }> => {
    const fieldConfigs = await getFieldConfigs(manageId)

    let businessData: BusinessData = {}
    if (type === 'edit' && id) {
      businessData = await getBusinessData(id, manageId, sensitivePwd)
    }

    return { fieldConfigs, businessData }
  }

  /**
   * 初始化字段选项（单选框、复选框）
   */
  const initFieldOptions = (field: LabelFieldConfig): void => {
    if (field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX) {
      fetchFieldOptions(field)
    }
  }

  /**
   * 处理字段数据初始化
   */
  const processFieldsData = (filteredData: FieldGroup[], businessData: BusinessData): void => {
    // 重置表单数据
    formData.value = {}

    filteredData.forEach((group) => {
      group.fields.forEach((field: LabelFieldConfig) => {
        // 修正字段扩展配置类型
        FormDataProcessor.normalizeFieldExtConfig(field)

        // 初始化字段选项
        initFieldOptions(field)

        // 初始化表单数据
        FormDataProcessor.initFieldFormData(field, businessData, formData.value)
      })
    })

    // 设置字段组
    fieldGroups.value = filteredData

    // 生成验证规则
    const allFields = FormDataProcessor.normalizeFields(
      filteredData.flatMap((group: FieldGroup) => group.fields)
    )
    Object.assign(formRules, generateFormRules(allFields))
  }

  return {
    fieldGroups,
    formData,
    formRules,
    fieldOptionsMap,
    getFieldConfigs,
    getBusinessData,
    fetchFieldOptions,
    getFieldOptions,
    generateFormRules,
    getFormConfigData,
    getBaseConfig,
    fetchConfigAndBusinessData,
    initFieldOptions,
    processFieldsData
  }
}
