import type { FieldGroup, FieldConfigWithVisibility } from '@/config/constants/types'

/**
 * 字段可见性管理 Hook
 */
export function useFieldVisibility() {
  /**
   * 判断字段是否应该显示
   */
  const isFieldVisible = (field: FieldConfigWithVisibility, fieldGroups: FieldGroup[], formData: Record<string, any>): boolean => {
    const linkage = field.linkage
    if (!linkage || !linkage.enabled) {
      return true // 没有关联配置或未启用，默认显示
    }

    const targetFieldId = linkage.targetFieldId
    const targetFieldValue = linkage.targetFieldValue
    const condition = linkage.condition

    if (!targetFieldId) {
      return true
    }

    // 找到目标字段的值
    let targetValue = null
    for (const group of fieldGroups) {
      for (const f of group.fields) {
        if (f.id === targetFieldId) {
          targetValue = formData[f.code]
          break
        }
      }
      if (targetValue !== null) break
    }

    // 根据条件判断是否显示
    switch (condition) {
      case 'equals':
        return targetValue === targetFieldValue
      case 'notEquals':
        return targetValue !== targetFieldValue
      case 'contains':
      case 'notContains':
      case 'startsWith':
      case 'endsWith':
        return String(targetValue).includes(String(targetFieldValue))
      default:
        return true
    }
  }

  return {
    isFieldVisible
  }
}
