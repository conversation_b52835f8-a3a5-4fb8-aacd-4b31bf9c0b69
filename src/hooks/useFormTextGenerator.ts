import { dateFormatter } from '@/utils/formatter'
import { FieldType } from '@/config/constants/enums/field'
import type { LabelFieldConfig } from '@/config/constants/enums/fieldDefault'

/**
 * 表单文本生成 Hook
 */
export function useFormTextGenerator() {
  /**
   * 生成表单字段的显示文本
   */
  const generateFormText = (
    field: LabelFieldConfig, 
    formData: Record<string, any>, 
    fieldOptionsMap: Map<string, any[]>
  ) => {
    const { fieldType, code } = field
    
    if (fieldType === FieldType.TEXT || fieldType === FieldType.NUMBER) {
      return formData[code]
    }
    
    if (fieldType === FieldType.DATE) {
      return dateFormatter(undefined, undefined, formData[code])
    }
    
    if (fieldType === FieldType.DATE_RANGE) {
      const code2 = field.fieldConfExtDOList.find((item: any) => item.name === 'code2')?.value || ''
      return [
        dateFormatter(undefined, undefined, formData[code]), 
        dateFormatter(undefined, undefined, formData[code2])
      ].join(' - ')
    }
    
    if (fieldType === FieldType.RADIO || fieldType === FieldType.CHECKBOX) {
      const value = String(formData[code] || '')
      const valueArr = value.split(',').filter(Boolean)
      
      return valueArr
        .map((value) => fieldOptionsMap.get(code)?.find((item) => item.value === value)?.label)
        .filter(Boolean)
        .join(',')
    }
    
    if (fieldType === FieldType.ATTACHMENT) {
      return formData[code]
    }
    
    // TODO: 处理标签类型
    if (fieldType === FieldType.TAG) {
      return formData[code]
    }
    
    return formData[code]
  }

  return {
    generateFormText
  }
}
